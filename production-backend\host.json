{"version": "2.0", "functionTimeout": "00:10:00", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.21.0, 5.0.0)"}, "extensions": {"http": {"routePrefix": "", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true}}, "cors": {"allowedOrigins": ["http://localhost:3000"], "supportCredentials": true}, "functionAppScaleLimit": 200, "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 3, "minimumInterval": "00:00:02", "maximumInterval": "00:00:30"}, "healthMonitor": {"enabled": true, "healthCheckInterval": "00:00:10", "healthCheckWindow": "00:02:00", "healthCheckThreshold": 6, "counterThreshold": 0.8}}