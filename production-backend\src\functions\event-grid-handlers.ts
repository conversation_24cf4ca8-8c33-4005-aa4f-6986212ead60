/**
 * Enhanced Event Grid Handlers for Azure Functions
 * Production-ready Event Grid event processing with comprehensive error handling,
 * event validation, replay mechanisms, and Azure best practices
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app, EventGridEvent as AzureFunctionEventGridEvent } from '@azure/functions';
import { EventGridEvent } from '@azure/eventgrid';
import { DefaultAzureCredential } from '@azure/identity';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventGridIntegration } from '../shared/services/event-grid-integration';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';
import { signalRService } from '../shared/services/signalr';
import { v4 as uuidv4 } from 'uuid';

// Enhanced Event Grid metrics
interface EventGridMetrics {
  eventsReceived: number;
  eventsProcessed: number;
  eventsFailed: number;
  eventsReplayed: number;
  averageProcessingTime: number;
  lastProcessedTime: Date;
  eventsByType: Map<string, number>;
  failuresByType: Map<string, number>;
}

let metrics: EventGridMetrics = {
  eventsReceived: 0,
  eventsProcessed: 0,
  eventsFailed: 0,
  eventsReplayed: 0,
  averageProcessingTime: 0,
  lastProcessedTime: new Date(),
  eventsByType: new Map(),
  failuresByType: new Map()
};

// Event processing state tracking
interface EventProcessingState {
  eventId: string;
  eventType: string;
  status: 'processing' | 'completed' | 'failed' | 'replaying';
  startTime: Date;
  endTime?: Date;
  retryCount: number;
  lastError?: string;
  correlationId: string;
}

const processingStates: Map<string, EventProcessingState> = new Map();

// Initialize Service Bus service
const serviceBusService = new ServiceBusEnhancedService();
serviceBusService.initialize().catch(error => {
  logger.error('Failed to initialize Service Bus service in event-grid-handlers', { error });
});

// Initialize SignalR service
const signalRServiceInstance = signalRService.getInstance();
signalRServiceInstance.initialize().catch(error => {
  logger.error('Failed to initialize SignalR service in event-grid-handlers', { error });
});

/**
 * Enhanced Event Types enum with comprehensive business events
 */
enum EventType {
  // Document Lifecycle Events
  DOCUMENT_UPLOADED = 'Document.Uploaded',
  DOCUMENT_UPLOAD_COMPLETED = 'Document.UploadCompleted',
  DOCUMENT_CLASSIFICATION_COMPLETED = 'Document.ClassificationCompleted',
  DOCUMENT_AI_ANALYSIS_COMPLETED = 'Document.AIAnalysisCompleted',
  DOCUMENT_PROCESSING_COMPLETED = 'Document.ProcessingCompleted',
  DOCUMENT_PROCESSING_FAILED = 'Document.ProcessingFailed',
  DOCUMENT_QUALITY_VALIDATED = 'Document.QualityValidated',
  DOCUMENT_APPROVED = 'Document.Approved',
  DOCUMENT_REJECTED = 'Document.Rejected',
  DOCUMENT_SHARED = 'Document.Shared',
  DOCUMENT_DELETED = 'Document.Deleted',
  DOCUMENT_ARCHIVED = 'Document.Archived',

  // AI Operations Events
  AI_OPERATION_STARTED = 'AI.OperationStarted',
  AI_OPERATION_COMPLETED = 'AI.OperationCompleted',
  AI_OPERATION_FAILED = 'AI.OperationFailed',
  AI_BATCH_COMPLETED = 'AI.BatchCompleted',
  AI_MODEL_UPDATED = 'AI.ModelUpdated',

  // Workflow Events
  WORKFLOW_STARTED = 'Workflow.Started',
  WORKFLOW_STEP_STARTED = 'Workflow.StepStarted',
  WORKFLOW_STEP_COMPLETED = 'Workflow.StepCompleted',
  WORKFLOW_STEP_FAILED = 'Workflow.StepFailed',
  WORKFLOW_COMPLETED = 'Workflow.Completed',
  WORKFLOW_FAILED = 'Workflow.Failed',
  WORKFLOW_CANCELLED = 'Workflow.Cancelled',
  WORKFLOW_TEMPLATE_UPDATED = 'Workflow.TemplateUpdated',

  // User Events
  USER_REGISTERED = 'User.Registered',
  USER_UPDATED = 'User.Updated',
  USER_DELETED = 'User.Deleted',
  USER_LOGIN = 'User.Login',
  USER_LOGOUT = 'User.Logout',
  USER_ACTIVITY_TRACKED = 'User.ActivityTracked',
  USER_PERMISSIONS_UPDATED = 'User.PermissionsUpdated',

  // Organization Events
  ORGANIZATION_CREATED = 'Organization.Created',
  ORGANIZATION_UPDATED = 'Organization.Updated',
  ORGANIZATION_DELETED = 'Organization.Deleted',
  ORGANIZATION_MEMBER_ADDED = 'Organization.MemberAdded',
  ORGANIZATION_MEMBER_REMOVED = 'Organization.MemberRemoved',
  ORGANIZATION_SETTINGS_UPDATED = 'Organization.SettingsUpdated',

  // Project Events
  PROJECT_CREATED = 'Project.Created',
  PROJECT_UPDATED = 'Project.Updated',
  PROJECT_DELETED = 'Project.Deleted',
  PROJECT_MEMBER_ADDED = 'Project.MemberAdded',
  PROJECT_MEMBER_REMOVED = 'Project.MemberRemoved',

  // Notification Events
  NOTIFICATION_CREATED = 'Notification.Created',
  NOTIFICATION_SENT = 'Notification.Sent',
  NOTIFICATION_DELIVERED = 'Notification.Delivered',
  NOTIFICATION_FAILED = 'Notification.Failed',
  NOTIFICATION_READ = 'Notification.Read',
  NOTIFICATION_PREFERENCES_UPDATED = 'Notification.PreferencesUpdated',

  // System Events
  SYSTEM_HEALTH_CHECK = 'System.HealthCheck',
  SYSTEM_BACKUP_COMPLETED = 'System.BackupCompleted',
  SYSTEM_MAINTENANCE_STARTED = 'System.MaintenanceStarted',
  SYSTEM_MAINTENANCE_COMPLETED = 'System.MaintenanceCompleted',

  // Performance & Monitoring Events
  PERFORMANCE_ALERT = 'Performance.Alert',
  PERFORMANCE_THRESHOLD_EXCEEDED = 'Performance.ThresholdExceeded',
  PERFORMANCE_METRICS_COLLECTED = 'Performance.MetricsCollected',

  // Analytics Events
  ANALYTICS_GENERATED = 'Analytics.Generated',
  ANALYTICS_REPORT_CREATED = 'Analytics.ReportCreated',
  ANALYTICS_DASHBOARD_UPDATED = 'Analytics.DashboardUpdated',

  // Security Events
  SECURITY_THREAT_DETECTED = 'Security.ThreatDetected',
  SECURITY_ACCESS_DENIED = 'Security.AccessDenied',
  SECURITY_AUDIT_LOG_CREATED = 'Security.AuditLogCreated',

  // Integration Events
  INTEGRATION_WEBHOOK_RECEIVED = 'Integration.WebhookReceived',
  INTEGRATION_API_CALL_COMPLETED = 'Integration.APICallCompleted',
  INTEGRATION_SYNC_COMPLETED = 'Integration.SyncCompleted',

  // Storage Events (Azure Storage)
  STORAGE_BLOB_CREATED = 'Microsoft.Storage.BlobCreated',
  STORAGE_BLOB_DELETED = 'Microsoft.Storage.BlobDeleted',
  STORAGE_BLOB_RENAMED = 'Microsoft.Storage.BlobRenamed'
}

/**
 * Enhanced Event Publishing with retry logic and failure handling
 */
async function publishEvent(
  eventType: EventType,
  subject: string,
  data: any,
  dataVersion: string = '1.0',
  correlationId?: string,
  retryCount: number = 0
): Promise<string | null> {
  const maxRetries = 3;
  const eventId = uuidv4();
  const publishCorrelationId = correlationId || uuidv4();

  try {
    // Add metadata to event data
    const enhancedData = {
      ...data,
      eventId,
      correlationId: publishCorrelationId,
      publishedAt: new Date().toISOString(),
      retryCount,
      source: 'azure-functions-event-grid'
    };

    const publishedEventId = await eventGridIntegration.publishEvent({
      eventType: eventType,
      subject: subject,
      data: enhancedData,
      dataVersion: dataVersion
    });

    if (publishedEventId) {
      // Update metrics
      metrics.eventsByType.set(eventType, (metrics.eventsByType.get(eventType) || 0) + 1);

      logger.info('Event published to Event Grid successfully', {
        eventType,
        subject,
        eventId: publishedEventId,
        correlationId: publishCorrelationId,
        retryCount
      });

      return publishedEventId;
    } else {
      logger.warn('Event was filtered or failed to publish', {
        eventType,
        subject,
        eventId,
        correlationId: publishCorrelationId
      });
      return null;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    logger.error('Failed to publish event to Event Grid', {
      eventType,
      subject,
      eventId,
      correlationId: publishCorrelationId,
      retryCount,
      error: errorMessage
    });

    // Update failure metrics
    metrics.failuresByType.set(eventType, (metrics.failuresByType.get(eventType) || 0) + 1);

    // Retry logic for transient failures
    if (retryCount < maxRetries && isTransientEventError(error)) {
      const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
      logger.info(`Retrying event publication after ${delay}ms`, {
        eventType,
        subject,
        eventId,
        retryCount: retryCount + 1
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      return await publishEvent(eventType, subject, data, dataVersion, publishCorrelationId, retryCount + 1);
    }

    // Store failed event for manual retry
    await storeFailedEvent({
      eventId,
      eventType,
      subject,
      data,
      dataVersion,
      correlationId: publishCorrelationId,
      error: errorMessage,
      retryCount,
      failedAt: new Date().toISOString()
    });

    throw error;
  }
}

/**
 * Store failed event for later retry
 */
async function storeFailedEvent(failedEvent: any): Promise<void> {
  try {
    await db.createItem('failed-events', {
      id: `failed-event-${failedEvent.eventId}`,
      ...failedEvent,
      partitionKey: 'failed-events'
    });

    logger.info('Failed event stored for retry', {
      eventId: failedEvent.eventId,
      eventType: failedEvent.eventType
    });
  } catch (error) {
    logger.error('Failed to store failed event', {
      eventId: failedEvent.eventId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Determine if error is transient and should be retried
 */
function isTransientEventError(error: unknown): boolean {
  const errorMessage = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase();

  const transientPatterns = [
    'timeout',
    'throttled',
    'rate limit',
    'service unavailable',
    'network error',
    'temporary failure',
    'connection reset'
  ];

  return transientPatterns.some(pattern => errorMessage.includes(pattern));
}

/**
 * Replay failed events
 */
async function replayFailedEvents(): Promise<void> {
  try {
    const failedEvents = await db.queryItems<any>('failed-events',
      'SELECT * FROM c WHERE c.partitionKey = "failed-events" AND c.retryCount < 5'
    );

    for (const failedEvent of failedEvents) {
      try {
        await publishEvent(
          failedEvent.eventType,
          failedEvent.subject,
          failedEvent.data,
          failedEvent.dataVersion,
          failedEvent.correlationId,
          failedEvent.retryCount
        );

        // Remove from failed events if successful
        await db.deleteItem('failed-events', failedEvent.id, 'failed-events');
        metrics.eventsReplayed++;

      } catch (error) {
        logger.error('Failed to replay event', {
          eventId: failedEvent.eventId,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  } catch (error) {
    logger.error('Error during event replay', {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Handle Event Grid webhook events
 */
async function handleEventGridWebhook(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const body = await request.text();
    const events = JSON.parse(body);

    // Handle validation handshake
    if (events.length === 1 && events[0].eventType === 'Microsoft.EventGrid.SubscriptionValidationEvent') {
      const validationCode = events[0].data.validationCode;
      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { validationResponse: validationCode }
      }, request);
    }

    // Process events
    for (const event of events) {
      await processEventGridEvent(event);
    }

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { message: 'Events processed successfully' }
    }, request);

  } catch (error) {
    logger.error('Error handling Event Grid webhook', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to process events' }
    }, request);
  }
}

/**
 * Enhanced Event Processing with comprehensive validation and error handling
 */
async function processEventGridEvent(event: any, context?: InvocationContext): Promise<void> {
  const startTime = Date.now();
  const correlationId = event.data?.correlationId || context?.invocationId || uuidv4();

  // Track processing state
  const processingState: EventProcessingState = {
    eventId: event.id,
    eventType: event.eventType,
    status: 'processing',
    startTime: new Date(),
    retryCount: event.data?.retryCount || 0,
    correlationId
  };

  processingStates.set(event.id, processingState);
  metrics.eventsReceived++;

  logger.info('Processing Event Grid event with enhanced handling', {
    eventType: event.eventType,
    subject: event.subject,
    eventId: event.id,
    correlationId,
    retryCount: processingState.retryCount
  });

  try {
    // Validate event structure
    if (!validateEventGridEvent(event)) {
      throw new Error('Event validation failed');
    }

    // Check for duplicate processing
    const isDuplicate = await checkDuplicateEvent(event.id);
    if (isDuplicate) {
      logger.info('Duplicate event detected, skipping processing', {
        eventId: event.id,
        eventType: event.eventType
      });
      return;
    }

    // Mark event as being processed
    await markEventAsProcessing(event.id, correlationId);

    // Process based on event type with comprehensive error handling
    switch (event.eventType) {
      // Azure Storage Events
      case EventType.STORAGE_BLOB_CREATED:
      case 'Microsoft.Storage.BlobCreated':
        await handleEnhancedBlobCreatedEvent(event, correlationId);
        break;
      case EventType.STORAGE_BLOB_DELETED:
      case 'Microsoft.Storage.BlobDeleted':
        await handleEnhancedBlobDeletedEvent(event, correlationId);
        break;

      // Document Lifecycle Events
      case EventType.DOCUMENT_UPLOADED:
        await handleDocumentUploadedEvent(event, correlationId);
        break;
      case EventType.DOCUMENT_UPLOAD_COMPLETED:
        await handleDocumentUploadCompletedEvent(event, correlationId);
        break;
      case EventType.DOCUMENT_AI_ANALYSIS_COMPLETED:
        await handleDocumentAIAnalysisCompletedEvent(event, correlationId);
        break;
      case EventType.DOCUMENT_PROCESSING_COMPLETED:
        await handleDocumentProcessingCompletedEvent(event, correlationId);
        break;
      case EventType.DOCUMENT_PROCESSING_FAILED:
        await handleDocumentProcessingFailedEvent(event, correlationId);
        break;
      case EventType.DOCUMENT_SHARED:
        await handleDocumentSharedEvent(event, correlationId);
        break;

      // AI Operations Events
      case EventType.AI_OPERATION_COMPLETED:
        await handleAIOperationCompletedEvent(event, correlationId);
        break;
      case EventType.AI_OPERATION_FAILED:
        await handleAIOperationFailedEvent(event, correlationId);
        break;

      // Workflow Events
      case EventType.WORKFLOW_STARTED:
        await handleWorkflowStartedEvent(event, correlationId);
        break;
      case EventType.WORKFLOW_STEP_COMPLETED:
        await handleWorkflowStepCompletedEvent(event, correlationId);
        break;
      case EventType.WORKFLOW_COMPLETED:
        await handleWorkflowCompletedEvent(event, correlationId);
        break;
      case EventType.WORKFLOW_FAILED:
        await handleWorkflowFailedEvent(event, correlationId);
        break;

      // User Events
      case EventType.USER_REGISTERED:
        await handleUserRegisteredEvent(event, correlationId);
        break;
      case EventType.USER_ACTIVITY_TRACKED:
        await handleUserActivityTrackedEvent(event, correlationId);
        break;

      // Notification Events
      case EventType.NOTIFICATION_DELIVERED:
        await handleNotificationDeliveredEvent(event, correlationId);
        break;
      case EventType.NOTIFICATION_FAILED:
        await handleNotificationFailedEvent(event, correlationId);
        break;

      // System Events
      case EventType.SYSTEM_HEALTH_CHECK:
        await handleSystemHealthCheckEvent(event, correlationId);
        break;
      case EventType.PERFORMANCE_ALERT:
        await handlePerformanceAlertEvent(event, correlationId);
        break;
      case EventType.ANALYTICS_GENERATED:
        await handleAnalyticsGeneratedEvent(event, correlationId);
        break;

      default:
        logger.info('Unhandled event type', {
          eventType: event.eventType,
          eventId: event.id,
          correlationId
        });
    }

    // Mark event as completed
    processingState.status = 'completed';
    processingState.endTime = new Date();
    await markEventAsCompleted(event.id, correlationId);

    metrics.eventsProcessed++;
    updateProcessingTimeMetrics(Date.now() - startTime);

    logger.info('Event processed successfully', {
      eventId: event.id,
      eventType: event.eventType,
      correlationId,
      processingTime: Date.now() - startTime
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Update processing state
    processingState.status = 'failed';
    processingState.endTime = new Date();
    processingState.lastError = errorMessage;

    metrics.eventsFailed++;

    logger.error('Error processing event', {
      eventType: event.eventType,
      eventId: event.id,
      correlationId,
      error: errorMessage,
      processingTime: Date.now() - startTime,
      retryCount: processingState.retryCount
    });

    // Store failed event for analysis
    await storeFailedEventProcessing({
      eventId: event.id,
      eventType: event.eventType,
      subject: event.subject,
      data: event.data,
      error: errorMessage,
      correlationId,
      retryCount: processingState.retryCount,
      failedAt: new Date().toISOString()
    });

    throw error;
  } finally {
    // Clean up processing state after some time
    setTimeout(() => {
      processingStates.delete(event.id);
    }, 300000); // 5 minutes
  }
}

/**
 * Event Processing Utility Functions
 */
async function checkDuplicateEvent(eventId: string): Promise<boolean> {
  try {
    const cacheKey = `event:processed:${eventId}`;
    const exists = await redis.exists(cacheKey);
    return Boolean(exists);
  } catch (error) {
    logger.error('Error checking duplicate event', {
      eventId,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

async function markEventAsProcessing(eventId: string, correlationId: string): Promise<void> {
  try {
    const cacheKey = `event:processing:${eventId}`;
    await redis.setex(cacheKey, 3600, JSON.stringify({
      status: 'processing',
      correlationId,
      startedAt: new Date().toISOString()
    }));
  } catch (error) {
    logger.error('Error marking event as processing', {
      eventId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function markEventAsCompleted(eventId: string, correlationId: string): Promise<void> {
  try {
    const processedKey = `event:processed:${eventId}`;
    const processingKey = `event:processing:${eventId}`;

    // Mark as processed (longer TTL for duplicate detection)
    await redis.setex(processedKey, 86400, JSON.stringify({
      status: 'completed',
      correlationId,
      completedAt: new Date().toISOString()
    }));

    // Remove from processing
    await redis.del(processingKey);
  } catch (error) {
    logger.error('Error marking event as completed', {
      eventId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function storeFailedEventProcessing(failedEvent: any): Promise<void> {
  try {
    await db.createItem('failed-event-processing', {
      id: `failed-processing-${failedEvent.eventId}-${Date.now()}`,
      ...failedEvent,
      partitionKey: 'failed-processing'
    });
  } catch (error) {
    logger.error('Error storing failed event processing', {
      eventId: failedEvent.eventId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

function updateProcessingTimeMetrics(processingTime: number): void {
  const currentAvg = metrics.averageProcessingTime;
  const currentCount = metrics.eventsProcessed;

  // Calculate new average processing time
  metrics.averageProcessingTime = ((currentAvg * (currentCount - 1)) + processingTime) / currentCount;
  metrics.lastProcessedTime = new Date();
}

/**
 * Enhanced Blob Created Event Handler
 */
async function handleEnhancedBlobCreatedEvent(event: any, correlationId: string): Promise<void> {
  const blobUrl = event.subject;
  const blobName = blobUrl.split('/').pop();
  const containerName = blobUrl.split('/').slice(-2, -1)[0];

  logger.info('Enhanced blob created event received', {
    blobName,
    blobUrl,
    containerName,
    correlationId
  });

  try {
    // Process based on container type
    switch (containerName) {
      case 'documents':
        await handleDocumentBlobCreated(blobName, blobUrl, event, correlationId);
        break;
      case 'thumbnails':
        await handleThumbnailBlobCreated(blobName, blobUrl, event, correlationId);
        break;
      case 'processed':
        await handleProcessedDocumentBlobCreated(blobName, blobUrl, event, correlationId);
        break;
      default:
        logger.info('Blob created in unmonitored container', {
          containerName,
          blobName,
          correlationId
        });
    }
  } catch (error) {
    logger.error('Error handling blob created event', {
      blobName,
      blobUrl,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function handleDocumentBlobCreated(
  blobName: string,
  blobUrl: string,
  event: any,
  correlationId: string
): Promise<void> {
  // Extract document ID from blob name
  const documentId = blobName.split('.')[0];

  // Trigger document upload completed event
  await publishEvent(
    EventType.DOCUMENT_UPLOAD_COMPLETED,
    `documents/${documentId}/upload-completed`,
    {
      documentId,
      blobName,
      blobUrl,
      fileSize: event.data?.contentLength || 0,
      contentType: event.data?.contentType || 'application/octet-stream',
      uploadedAt: event.eventTime,
      autoProcessing: true,
      correlationId
    },
    '1.0',
    correlationId
  );

  // Send to Service Bus for document processing
  const success = await serviceBusService.sendToQueue('document-processing', {
    body: {
      documentId,
      action: 'upload-completed',
      organizationId: extractOrganizationFromPath(blobUrl),
      userId: extractUserFromPath(blobUrl),
      blobName,
      blobUrl,
      autoProcess: true,
      correlationId
    },
    correlationId,
    messageId: `doc-upload-${documentId}-${Date.now()}`
  });

  if (!success) {
    logger.error('Failed to queue document processing', { documentId });
  }
}

async function handleThumbnailBlobCreated(
  blobName: string,
  blobUrl: string,
  event: any,
  correlationId: string
): Promise<void> {
  const documentId = blobName.split('_thumbnail')[0];

  // Update document with thumbnail URL
  try {
    const documents = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.id = @documentId',
      [{ name: '@documentId', value: documentId }]
    );

    if (documents.length > 0) {
      const document = documents[0];
      await db.updateItem('documents', {
        ...document,
        thumbnailUrl: blobUrl,
        thumbnailGeneratedAt: new Date().toISOString(),
        correlationId
      });

      // Publish thumbnail generated event
      await publishEvent(
        EventType.DOCUMENT_PROCESSING_COMPLETED,
        `documents/${documentId}/thumbnail-generated`,
        {
          documentId,
          thumbnailUrl: blobUrl,
          generatedAt: new Date().toISOString(),
          correlationId
        },
        '1.0',
        correlationId
      );
    }
  } catch (error) {
    logger.error('Error updating document with thumbnail', {
      documentId,
      blobName,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function handleProcessedDocumentBlobCreated(
  blobName: string,
  blobUrl: string,
  event: any,
  correlationId: string
): Promise<void> {
  const documentId = blobName.split('_processed')[0];

  // Update document with processed file URL
  try {
    const documents = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.id = @documentId',
      [{ name: '@documentId', value: documentId }]
    );

    if (documents.length > 0) {
      const document = documents[0];
      await db.updateItem('documents', {
        ...document,
        processedFileUrl: blobUrl,
        processedAt: new Date().toISOString(),
        status: 'processed',
        correlationId
      });

      // Publish document processed event
      await publishEvent(
        EventType.DOCUMENT_PROCESSING_COMPLETED,
        `documents/${documentId}/processing-completed`,
        {
          documentId,
          processedFileUrl: blobUrl,
          processedAt: new Date().toISOString(),
          correlationId
        },
        '1.0',
        correlationId
      );
    }
  } catch (error) {
    logger.error('Error updating document with processed file', {
      documentId,
      blobName,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

function extractOrganizationFromPath(blobUrl: string): string {
  // Extract organization ID from blob path pattern: /organizations/{orgId}/documents/...
  const pathParts = blobUrl.split('/');
  const orgIndex = pathParts.findIndex(part => part === 'organizations');
  return orgIndex !== -1 && orgIndex + 1 < pathParts.length ? pathParts[orgIndex + 1] : 'default';
}

function extractUserFromPath(blobUrl: string): string {
  // Extract user ID from blob path pattern: /users/{userId}/documents/...
  const pathParts = blobUrl.split('/');
  const userIndex = pathParts.findIndex(part => part === 'users');
  return userIndex !== -1 && userIndex + 1 < pathParts.length ? pathParts[userIndex + 1] : 'system';
}

/**
 * Enhanced Blob Deleted Event Handler
 */
async function handleEnhancedBlobDeletedEvent(event: any, correlationId: string): Promise<void> {
  const blobUrl = event.subject;
  const blobName = blobUrl.split('/').pop();
  const containerName = blobUrl.split('/').slice(-2, -1)[0];

  logger.info('Enhanced blob deleted event received', {
    blobName,
    blobUrl,
    containerName,
    correlationId
  });

  try {
    // Update document status in database
    const documents = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.blobName = @blobName',
      [{ name: '@blobName', value: blobName }]
    );

    for (const doc of documents) {
      await db.updateItem('documents', {
        ...doc,
        status: 'deleted',
        deletedAt: new Date().toISOString(),
        correlationId
      });

      // Publish document deleted event
      await publishEvent(
        EventType.DOCUMENT_DELETED,
        `documents/${doc.id}/deleted`,
        {
          documentId: doc.id,
          blobName,
          blobUrl,
          deletedAt: new Date().toISOString(),
          organizationId: doc.organizationId,
          userId: doc.userId,
          correlationId
        },
        '1.0',
        correlationId
      );

      // Clean up related cache entries
      await cleanupDocumentCache(doc.id);
    }

    logger.info('Document status updated after blob deletion', {
      blobName,
      documentsUpdated: documents.length,
      correlationId
    });

  } catch (error) {
    logger.error('Error handling blob deleted event', {
      blobName,
      blobUrl,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function cleanupDocumentCache(documentId: string): Promise<void> {
  try {
    const cacheKeys = [
      `doc:${documentId}:content`,
      `doc:${documentId}:metadata`,
      `doc:${documentId}:status`,
      `doc:${documentId}:analysis`,
      `doc:${documentId}:thumbnail`
    ];

    for (const key of cacheKeys) {
      await redis.del(key);
    }

    logger.info('Document cache cleaned up', { documentId });
  } catch (error) {
    logger.error('Error cleaning up document cache', {
      documentId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Enhanced Document Event Handlers
 */
async function handleDocumentUploadCompletedEvent(event: any, correlationId: string): Promise<void> {
  const { documentId, blobName, blobUrl, organizationId, userId, autoProcessing } = event.data;

  logger.info('Document upload completed event received', {
    documentId,
    blobName,
    organizationId,
    correlationId
  });

  try {
    // Update document search index
    await updateDocumentSearchIndex(documentId, organizationId);

    // Trigger real-time UI update via SignalR
    await signalRServiceInstance.sendToUser(userId, {
      target: 'documentUploadCompleted',
      arguments: [{
        documentId,
        blobName,
        status: 'uploaded',
        timestamp: event.eventTime,
        correlationId
      }]
    });

    // Update user activity tracking
    await trackUserActivity(userId, 'document_uploaded', {
      documentId,
      organizationId,
      blobName,
      timestamp: event.eventTime,
      correlationId
    });

    // If auto-processing enabled, trigger AI analysis
    if (autoProcessing) {
      const analysisSuccess = await serviceBusService.sendToQueue('ai-operations', {
        body: {
          operationId: uuidv4(),
          operationType: 'document-analysis',
          documentId,
          organizationId,
          userId,
          configuration: {
            analysisTypes: ['layout', 'key_value', 'table_extraction'],
            includeOCR: true,
            includeClassification: true
          },
          priority: 'normal',
          correlationId
        },
        correlationId,
        messageId: `ai-analysis-${documentId}-${Date.now()}`
      });

      if (!analysisSuccess) {
        logger.error('Failed to queue auto-analysis', { documentId });
      }
    }

    // Update analytics
    await updateDocumentAnalytics(organizationId, 'upload_completed', {
      documentId,
      userId,
      timestamp: event.eventTime,
      correlationId
    });

    logger.info('Document upload completed event processed successfully', {
      documentId,
      organizationId,
      userId,
      correlationId
    });

  } catch (error) {
    logger.error('Error handling document upload completed event', {
      documentId,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function handleDocumentAIAnalysisCompletedEvent(event: any, correlationId: string): Promise<void> {
  const { documentId, analysisId, results, userId, organizationId, operationId } = event.data;

  logger.info('Document AI analysis completed event received', {
    documentId,
    analysisId,
    organizationId,
    correlationId
  });

  try {
    // Store analysis results in database
    await db.createItem('document-analyses', {
      id: analysisId,
      documentId,
      operationId,
      results,
      completedAt: event.eventTime,
      userId,
      organizationId,
      correlationId,
      partitionKey: organizationId
    });

    // Update document metadata with analysis results
    const document = await db.readItem('documents', documentId, organizationId);
    if (document) {
      await db.updateItem('documents', {
        id: document.id,
        ...document,
        analysisResults: results,
        analysisCompletedAt: event.eventTime,
        status: 'analyzed',
        correlationId
      });

      // Index content for RAG if substantial text extracted
      if (results.extractedText && results.extractedText.length > 500) {
        await indexDocumentForRAG(documentId, results, organizationId, correlationId);
      }

      // Trigger quality validation
      const qualityScore = await validateDocumentAnalysisQuality(results);

      // Route for approval if quality is below threshold
      if (qualityScore < 0.8) {
        await routeDocumentForManualReview(documentId, userId, {
          reason: 'low_quality_analysis',
          qualityScore,
          analysisId,
          organizationId,
          correlationId
        });
      } else {
        // Continue with automated workflow
        await continueAutomatedDocumentWorkflow(documentId, results, organizationId, correlationId);
      }

      // Update UI in real-time
      await signalRServiceInstance.sendToUser(userId, {
        target: 'aiAnalysisCompleted',
        arguments: [{
          documentId,
          analysisId,
          results: {
            confidence: results.overallConfidence,
            extractedFields: Object.keys(results.keyValuePairs || {}).length,
            tablesFound: results.tables?.length || 0,
            qualityScore
          },
          correlationId
        }]
      });

      // Invalidate related caches
      await invalidateDocumentCaches(documentId);

      logger.info('Document AI analysis completed event processed successfully', {
        documentId,
        analysisId,
        organizationId,
        qualityScore,
        correlationId
      });
    }

  } catch (error) {
    logger.error('Error handling document AI analysis completed event', {
      documentId,
      analysisId,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Document Processing Helper Functions
 */
async function updateDocumentSearchIndex(documentId: string, organizationId: string): Promise<void> {
  try {
    // This would integrate with Azure Cognitive Search
    logger.info('Document search index updated', { documentId, organizationId });
  } catch (error) {
    logger.error('Error updating document search index', {
      documentId,
      organizationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function trackUserActivity(userId: string, activity: string, data: any): Promise<void> {
  try {
    await db.createItem('user-activities', {
      id: uuidv4(),
      userId,
      activity,
      data,
      timestamp: new Date().toISOString(),
      partitionKey: userId
    });

    // Publish user activity event
    await publishEvent(
      EventType.USER_ACTIVITY_TRACKED,
      `users/${userId}/activity/${activity}`,
      {
        userId,
        activity,
        data,
        timestamp: new Date().toISOString()
      }
    );
  } catch (error) {
    logger.error('Error tracking user activity', {
      userId,
      activity,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function updateDocumentAnalytics(organizationId: string, event: string, data: any): Promise<void> {
  try {
    const analyticsData = {
      id: uuidv4(),
      organizationId,
      event,
      data,
      timestamp: new Date().toISOString(),
      partitionKey: organizationId
    };

    await db.createItem('document-analytics', analyticsData);

    // Publish analytics event
    await publishEvent(
      EventType.ANALYTICS_GENERATED,
      `organizations/${organizationId}/analytics/${event}`,
      analyticsData
    );
  } catch (error) {
    logger.error('Error updating document analytics', {
      organizationId,
      event,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function indexDocumentForRAG(
  documentId: string,
  analysisResults: any,
  organizationId: string,
  correlationId: string
): Promise<void> {
  try {
    // This would integrate with Azure Cognitive Search or vector database
    logger.info('Document indexed for RAG', {
      documentId,
      organizationId,
      textLength: analysisResults.extractedText?.length || 0,
      correlationId
    });
  } catch (error) {
    logger.error('Error indexing document for RAG', {
      documentId,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function validateDocumentAnalysisQuality(analysisResults: any): Promise<number> {
  try {
    let qualityScore = 0.5; // Base score

    // Check overall confidence
    if (analysisResults.overallConfidence > 0.8) {
      qualityScore += 0.3;
    } else if (analysisResults.overallConfidence > 0.6) {
      qualityScore += 0.2;
    }

    // Check text extraction quality
    if (analysisResults.extractedText && analysisResults.extractedText.length > 100) {
      qualityScore += 0.1;
    }

    // Check key-value pairs extraction
    if (analysisResults.keyValuePairs && Object.keys(analysisResults.keyValuePairs).length > 0) {
      qualityScore += 0.1;
    }

    return Math.min(qualityScore, 1.0);
  } catch (error) {
    logger.error('Error validating document analysis quality', {
      error: error instanceof Error ? error.message : String(error)
    });
    return 0.5; // Default score on error
  }
}

async function routeDocumentForManualReview(
  documentId: string,
  userId: string,
  reviewData: any
): Promise<void> {
  try {
    // Create manual review task
    const reviewTask = {
      id: uuidv4(),
      documentId,
      requestedBy: userId,
      reviewData,
      status: 'pending',
      priority: reviewData.qualityScore < 0.5 ? 'high' : 'normal',
      createdAt: new Date().toISOString(),
      partitionKey: reviewData.organizationId
    };

    await db.createItem('manual-review-tasks', reviewTask);

    // Send notification to reviewers
    const success = await serviceBusService.sendToQueue('notification-delivery', {
      body: {
        notificationId: uuidv4(),
        recipientId: 'review-team', // This would be actual reviewer IDs
        organizationId: reviewData.organizationId,
        channels: ['in_app', 'email'],
        priority: 'high',
        content: {
          title: 'Document Review Required',
          message: `Document ${documentId} requires manual review due to ${reviewData.reason}`,
          data: { documentId, reviewData }
        }
      },
      correlationId: reviewData.correlationId,
      messageId: `review-notification-${documentId}-${Date.now()}`
    });

    if (!success) {
      logger.error('Failed to queue review notification', { documentId, correlationId: reviewData.correlationId });
    }

    logger.info('Document routed for manual review', {
      documentId,
      reason: reviewData.reason,
      priority: reviewTask.priority,
      correlationId: reviewData.correlationId
    });

  } catch (error) {
    logger.error('Error routing document for manual review', {
      documentId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function continueAutomatedDocumentWorkflow(
  documentId: string,
  analysisResults: any,
  organizationId: string,
  correlationId: string
): Promise<void> {
  try {
    // Determine next workflow step based on document type
    const documentType = analysisResults.classification?.type || 'general';

    // Get appropriate workflow template
    const workflowTemplates = await db.queryItems<any>('workflow-templates',
      'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.documentType = @documentType AND c.isActive = true',
      [
        { name: '@organizationId', value: organizationId },
        { name: '@documentType', value: documentType }
      ]
    );

    if (workflowTemplates.length > 0) {
      const workflowTemplate = workflowTemplates[0];
      const executionId = uuidv4();

      // Start workflow execution
      const success = await serviceBusService.sendToQueue('workflow-orchestration', {
        body: {
          workflowId: workflowTemplate.id,
          executionId,
          action: 'start-workflow',
          organizationId,
          userId: 'system',
          stepData: {
            documentId,
            analysisResults,
            triggerType: 'ai-analysis-completed'
          },
          correlationId
        },
        correlationId,
        messageId: `workflow-start-${executionId}-${Date.now()}`
      });

      if (!success) {
        logger.error('Failed to queue workflow start', { workflowId: workflowTemplate.id, executionId, correlationId });
      }

      logger.info('Automated workflow started for document', {
        documentId,
        workflowId: workflowTemplate.id,
        executionId,
        documentType,
        correlationId
      });
    } else {
      logger.info('No automated workflow found for document type', {
        documentId,
        documentType,
        organizationId,
        correlationId
      });
    }

  } catch (error) {
    logger.error('Error continuing automated document workflow', {
      documentId,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function invalidateDocumentCaches(documentId: string): Promise<void> {
  try {
    const cacheKeys = [
      `doc:${documentId}:content`,
      `doc:${documentId}:metadata`,
      `doc:${documentId}:analysis`,
      `doc:${documentId}:status`
    ];

    for (const key of cacheKeys) {
      await redis.del(key);
    }

    logger.info('Document caches invalidated', { documentId });
  } catch (error) {
    logger.error('Error invalidating document caches', {
      documentId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Handle workflow completed event
 */
async function handleWorkflowCompletedEvent(event: any, correlationId?: string): Promise<void> {
  const { workflowId, status, completedBy } = event.data;

  logger.info('Workflow completed event received', { workflowId, status, correlationId });

  // Send notifications, update analytics, etc.
  await publishEvent(
    EventType.NOTIFICATION_SENT,
    `workflows/${workflowId}/completion`,
    {
      workflowId,
      status,
      completedBy,
      timestamp: new Date().toISOString()
    }
  );
}

/**
 * Handle document processed event
 */
async function handleDocumentProcessedEvent(event: any): Promise<void> {
  const { documentId, processingResults, processingTime } = event.data;

  logger.info('Document processed event received', { documentId, processingTime });

  try {
    // Update document status in database
    const documents = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.id = @documentId',
      [{ name: '@documentId', value: documentId }]
    );

    if (documents.length > 0) {
      const document = documents[0];
      await db.updateItem('documents', {
        ...document,
        status: 'processed',
        processedAt: new Date().toISOString(),
        processingResults
      });

      // Trigger analytics event
      await publishEvent(
        EventType.ANALYTICS_GENERATED,
        `documents/${documentId}/processing-analytics`,
        {
          documentId,
          processingTime,
          timestamp: new Date().toISOString()
        }
      );
    }
  } catch (error) {
    logger.error('Error handling document processed event', {
      documentId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Handle document shared event
 */
async function handleDocumentSharedEvent(event: any, correlationId?: string): Promise<void> {
  const { documentId, sharedWith, sharedBy, permissions } = event.data;

  logger.info('Document shared event received', { documentId, sharedWith, sharedBy, correlationId });

  try {
    // Send notification to shared users
    await publishEvent(
      EventType.NOTIFICATION_SENT,
      `documents/${documentId}/shared`,
      {
        documentId,
        sharedWith,
        sharedBy,
        permissions,
        notificationType: 'document_shared',
        timestamp: new Date().toISOString()
      }
    );
  } catch (error) {
    logger.error('Error handling document shared event', {
      documentId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Handle workflow started event
 */
async function handleWorkflowStartedEvent(event: any, correlationId: string): Promise<void> {
  const { workflowId, executionId, organizationId, userId, stepData } = event.data;

  logger.info('Enhanced workflow started event received', {
    workflowId,
    executionId,
    organizationId,
    userId,
    correlationId
  });

  try {
    // Update workflow execution status
    await updateWorkflowExecutionStatus(executionId, 'running', {
      startedAt: event.eventTime,
      correlationId
    });

    // Send notification to workflow participants
    await notifyWorkflowParticipants(workflowId, executionId, 'started', {
      organizationId,
      userId,
      stepData,
      correlationId
    });

    // Update analytics
    await updateWorkflowAnalytics(organizationId, 'workflow_started', {
      workflowId,
      executionId,
      userId,
      timestamp: event.eventTime,
      correlationId
    });

    // Cache workflow state for quick access
    await cacheWorkflowState(executionId, {
      status: 'running',
      currentStep: stepData?.currentStep,
      startedAt: event.eventTime,
      correlationId
    });

    logger.info('Workflow started event processed successfully', {
      workflowId,
      executionId,
      organizationId,
      correlationId
    });

  } catch (error) {
    logger.error('Error handling workflow started event', {
      workflowId,
      executionId,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Handle user registered event
 */
async function handleUserRegisteredEvent(event: any, correlationId: string): Promise<void> {
  const { userId, email, registrationMethod, organizationId } = event.data;

  logger.info('Enhanced user registered event received', {
    userId,
    email,
    registrationMethod,
    organizationId,
    correlationId
  });

  try {
    // Send welcome notification
    const success = await serviceBusService.sendToQueue('notification-delivery', {
      body: {
        notificationId: uuidv4(),
        recipientId: userId,
        organizationId: organizationId || 'default',
        channels: ['in_app', 'email'],
        priority: 'normal',
        content: {
          title: 'Welcome to the Platform!',
          message: 'Thank you for registering. Get started by uploading your first document.',
          data: { userId, registrationMethod }
        }
      },
      correlationId,
      messageId: `welcome-notification-${userId}-${Date.now()}`
    });

    if (!success) {
      logger.error('Failed to queue welcome notification', { userId, correlationId });
    }

    // Track user registration analytics
    await updateUserAnalytics(organizationId || 'default', 'user_registered', {
      userId,
      email,
      registrationMethod,
      registeredAt: event.eventTime,
      correlationId
    });

    // Initialize user preferences
    await initializeUserPreferences(userId, organizationId || 'default', correlationId);

    logger.info('User registered event processed successfully', {
      userId,
      email,
      organizationId,
      correlationId
    });

  } catch (error) {
    logger.error('Error handling user registered event', {
      userId,
      email,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Handle user activity tracked event
 */
async function handleUserActivityTrackedEvent(event: any, correlationId?: string): Promise<void> {
  const { userId, activity, activityData, organizationId, timestamp } = event.data;

  logger.info('User activity tracked event received', {
    userId,
    activity,
    organizationId,
    correlationId
  });

  try {
    // Update user engagement metrics
    await updateUserEngagementMetrics(userId, activity, organizationId);

    // Update user analytics
    await updateUserAnalytics(organizationId, activity, {
      userId,
      activity,
      activityData,
      timestamp: timestamp || new Date().toISOString()
    });

    // Check for activity-based triggers
    await checkActivityTriggers(userId, activity, activityData, organizationId, correlationId || '');

    logger.info('User activity tracked successfully', {
      userId,
      activity,
      organizationId,
      correlationId
    });

  } catch (error) {
    logger.error('Error handling user activity tracked event', {
      userId,
      activity,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Handle system health check event
 */
async function handleSystemHealthCheckEvent(event: any, correlationId?: string): Promise<void> {
  const eventData = event.data || {};
  const { timestamp, database, storage, redis, documentCount, activeWorkflows, queueDepth } = eventData;

  // Determine overall health status
  const services = { database, storage, redis };
  const serviceStatuses = Object.values(services).map((service: any) => service?.status).filter(Boolean);

  let overallHealthStatus = 'healthy';
  if (serviceStatuses.includes('unhealthy')) {
    overallHealthStatus = 'unhealthy';
  } else if (serviceStatuses.includes('unknown')) {
    overallHealthStatus = 'degraded';
  }

  logger.info('System health check event received', {
    overallHealthStatus,
    timestamp,
    services: serviceStatuses.length
  });

  try {
    // Store health metrics
    await db.createItem('system-health', {
      id: `health-${Date.now()}`,
      overallHealthStatus,
      services: {
        database: database || { status: 'unknown', responseTime: 0 },
        storage: storage || { status: 'unknown', responseTime: 0 },
        redis: redis || { status: 'unknown', responseTime: 0 }
      },
      metrics: {
        documentCount: documentCount || 0,
        activeWorkflows: activeWorkflows || 0,
        queueDepth: queueDepth || 0
      },
      timestamp: timestamp || new Date().toISOString(),
      createdAt: new Date().toISOString()
    });

    // Generate alert if unhealthy
    if (overallHealthStatus !== 'healthy') {
      await publishEvent(
        EventType.PERFORMANCE_ALERT,
        'system/health-alert',
        {
          alertType: 'health_check_failed',
          severity: overallHealthStatus === 'unhealthy' ? 'high' : 'medium',
          healthStatus: overallHealthStatus,
          services,
          metrics: {
            documentCount,
            activeWorkflows,
            queueDepth
          },
          timestamp: new Date().toISOString()
        }
      );
    }
  } catch (error) {
    logger.error('Error handling system health check event', {
      overallHealthStatus,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Handle performance alert event
 */
async function handlePerformanceAlertEvent(event: any, correlationId?: string): Promise<void> {
  const { alertType, severity, metrics } = event.data;

  logger.warn('Performance alert received', { alertType, severity, metrics, correlationId });

  try {
    // Store alert in database
    await db.createItem('performance-alerts', {
      id: `alert-${Date.now()}`,
      alertType,
      severity,
      metrics,
      timestamp: new Date().toISOString(),
      status: 'active'
    });

    // Send notification for high severity alerts
    if (severity === 'high' || severity === 'critical') {
      await publishEvent(
        EventType.NOTIFICATION_SENT,
        `system/performance-alert/${alertType}`,
        {
          alertType,
          severity,
          metrics,
          notificationType: 'performance_alert',
          timestamp: new Date().toISOString()
        }
      );
    }
  } catch (error) {
    logger.error('Error handling performance alert event', {
      alertType,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Handle analytics generated event
 */
async function handleAnalyticsGeneratedEvent(event: any, correlationId?: string): Promise<void> {
  const { analyticsType, data, generatedFor } = event.data;

  logger.info('Analytics generated event received', { analyticsType, generatedFor, correlationId });

  try {
    // Store analytics data
    await db.createItem('analytics-data', {
      id: `analytics-${Date.now()}`,
      analyticsType,
      data,
      generatedFor,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error handling analytics generated event', {
      analyticsType,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Handle notification sent event
 */
async function handleNotificationSentEvent(event: any): Promise<void> {
  const { notificationId, notificationType, recipient, status } = event.data;

  logger.info('Notification sent event received', { notificationId, notificationType, recipient });

  try {
    // Track notification analytics
    await db.createItem('notification-analytics', {
      id: `notification-${Date.now()}`,
      notificationId,
      notificationType,
      recipient,
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error handling notification sent event', {
      notificationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Publish custom event endpoint
 */
async function publishCustomEvent(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const body = await request.json() as any;
    const { eventType, subject, data } = body;

    if (!eventType || !subject || !data) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Missing required fields: eventType, subject, data' }
      }, request);
    }

    await publishEvent(eventType, subject, data);

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        message: 'Event published successfully',
        eventType,
        subject
      }
    }, request);

  } catch (error) {
    logger.error('Error publishing custom event', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to publish event' }
    }, request);
  }
}

/**
 * Native Event Grid trigger for storage events
 */
async function handleStorageEventGridTrigger(event: AzureFunctionEventGridEvent, context: InvocationContext): Promise<void> {
  logger.info('Storage Event Grid trigger activated', {
    eventType: event.eventType,
    subject: event.subject,
    eventId: event.id,
    invocationId: context.invocationId
  });

  try {
    await processEventGridEvent(event);
    logger.info('Storage event processed successfully', {
      eventId: event.id,
      eventType: event.eventType
    });
  } catch (error) {
    logger.error('Error processing storage event', {
      eventId: event.id,
      eventType: event.eventType,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error; // Re-throw to trigger retry mechanism
  }
}

/**
 * Native Event Grid trigger for custom application events
 */
async function handleCustomEventGridTrigger(event: AzureFunctionEventGridEvent, context: InvocationContext): Promise<void> {
  logger.info('Custom Event Grid trigger activated', {
    eventType: event.eventType,
    subject: event.subject,
    eventId: event.id,
    invocationId: context.invocationId
  });

  try {
    await processEventGridEvent(event);
    logger.info('Custom event processed successfully', {
      eventId: event.id,
      eventType: event.eventType
    });
  } catch (error) {
    logger.error('Error processing custom event', {
      eventId: event.id,
      eventType: event.eventType,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error; // Re-throw to trigger retry mechanism
  }
}

/**
 * Enhanced event validation
 */
function validateEventGridEvent(event: any): boolean {
  const requiredFields = ['id', 'eventType', 'subject', 'eventTime', 'data'];

  for (const field of requiredFields) {
    if (!event[field]) {
      logger.warn('Event validation failed - missing field', { field, eventId: event.id });
      return false;
    }
  }

  // Validate event time is not too old (24 hours)
  const eventTime = new Date(event.eventTime);
  const now = new Date();
  const hoursDiff = (now.getTime() - eventTime.getTime()) / (1000 * 60 * 60);

  if (hoursDiff > 24) {
    logger.warn('Event validation failed - event too old', {
      eventId: event.id,
      eventTime: event.eventTime,
      hoursDiff
    });
    return false;
  }

  return true;
}

/**
 * Enhanced event processing with validation and error handling
 */
async function processEventGridEventEnhanced(event: any): Promise<void> {
  // Validate event
  if (!validateEventGridEvent(event)) {
    logger.error('Event validation failed', { eventId: event.id });
    return; // Don't process invalid events
  }

  // Add processing metadata
  const processingMetadata = {
    processedAt: new Date().toISOString(),
    processingId: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    retryCount: event.retryCount || 0
  };

  logger.info('Processing Event Grid event with enhanced handling', {
    eventType: event.eventType,
    subject: event.subject,
    eventId: event.id,
    ...processingMetadata
  });

  try {
    // Process the event
    await processEventGridEvent(event);

    // Log successful processing
    logger.info('Event processed successfully', {
      eventId: event.id,
      eventType: event.eventType,
      ...processingMetadata
    });

  } catch (error) {
    logger.error('Error processing event with enhanced handling', {
      eventType: event.eventType,
      eventId: event.id,
      error: error instanceof Error ? error.message : String(error),
      ...processingMetadata
    });
    throw error;
  }
}

/**
 * Additional Helper Functions for Enhanced Event Grid Processing
 */
async function updateUserAnalytics(organizationId: string, event: string, data: any): Promise<void> {
  try {
    const analyticsData = {
      id: uuidv4(),
      organizationId,
      event,
      data,
      timestamp: new Date().toISOString(),
      partitionKey: organizationId
    };

    await db.createItem('user-analytics', analyticsData);

    // Publish analytics event
    await publishEvent(
      EventType.ANALYTICS_GENERATED,
      `organizations/${organizationId}/user-analytics/${event}`,
      analyticsData
    );
  } catch (error) {
    logger.error('Error updating user analytics', {
      organizationId,
      event,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function initializeUserPreferences(userId: string, organizationId: string, correlationId: string): Promise<void> {
  try {
    const defaultPreferences = {
      id: userId,
      userId,
      organizationId,
      notifications: {
        email: { enabled: true, frequency: 'immediate' },
        push: { enabled: false, frequency: 'daily' },
        inApp: { enabled: true, frequency: 'immediate' }
      },
      privacy: {
        profileVisibility: 'organization',
        activityTracking: true
      },
      createdAt: new Date().toISOString(),
      correlationId,
      partitionKey: organizationId
    };

    await db.createItem('user-preferences', defaultPreferences);
  } catch (error) {
    logger.error('Error initializing user preferences', {
      userId,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function updateUserEngagementMetrics(userId: string, activity: string, organizationId: string): Promise<void> {
  try {
    const engagementKey = `user:${userId}:engagement`;
    const currentMetrics = await redis.get(engagementKey);

    let metrics = currentMetrics ? JSON.parse(currentMetrics) : {
      totalActivities: 0,
      lastActivity: null,
      activitiesByType: {}
    };

    metrics.totalActivities++;
    metrics.lastActivity = new Date().toISOString();
    metrics.activitiesByType[activity] = (metrics.activitiesByType[activity] || 0) + 1;

    await redis.setex(engagementKey, 86400, JSON.stringify(metrics)); // 24 hours
  } catch (error) {
    logger.error('Error updating user engagement metrics', {
      userId,
      activity,
      organizationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function checkActivityTriggers(
  userId: string,
  activity: string,
  data: any,
  organizationId: string,
  correlationId: string
): Promise<void> {
  try {
    // Check for activity-based recommendations or triggers
    if (activity === 'document_uploaded' && data.documentCount === 1) {
      // First document uploaded - send tips notification
      const success = await serviceBusService.sendToQueue('notification-delivery', {
        body: {
          notificationId: uuidv4(),
          recipientId: userId,
          organizationId,
          channels: ['in_app'],
          priority: 'low',
          content: {
            title: 'Getting Started Tips',
            message: 'Great job uploading your first document! Here are some tips to get the most out of the platform.',
            data: { userId, trigger: 'first_document' }
          }
        },
        correlationId,
        messageId: `tips-notification-${userId}-${Date.now()}`
      });

      if (!success) {
        logger.error('Failed to queue tips notification', { userId, activity, correlationId });
      }
    }
  } catch (error) {
    logger.error('Error checking activity triggers', {
      userId,
      activity,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function notifySystemAdministrators(systemStatus: string, metrics: any, correlationId: string): Promise<void> {
  try {
    // Get system administrators
    const admins = await db.queryItems<any>('users',
      'SELECT * FROM c WHERE c.role = "system_admin" AND c.isActive = true'
    );

    for (const admin of admins) {
      const success = await serviceBusService.sendToQueue('notification-delivery', {
        body: {
          notificationId: uuidv4(),
          recipientId: admin.id,
          organizationId: 'system',
          channels: ['email', 'push'],
          priority: 'urgent',
          content: {
            title: 'System Health Alert',
            message: `System status is ${systemStatus}. Immediate attention required.`,
            data: { systemStatus, metrics, correlationId }
          }
        },
        correlationId,
        messageId: `system-alert-${admin.id}-${Date.now()}`
      });

      if (!success) {
        logger.error('Failed to queue system alert notification', { adminId: admin.id, systemStatus, correlationId });
      }
    }
  } catch (error) {
    logger.error('Error notifying system administrators', {
      systemStatus,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Enhanced Notification Event Handlers
 */
async function handleNotificationDeliveredEvent(event: any, correlationId: string): Promise<void> {
  const { notificationId, recipientId, organizationId, deliveryResults } = event.data;

  logger.info('Notification delivered event received', {
    notificationId,
    recipientId,
    organizationId,
    correlationId
  });

  try {
    // Update notification analytics
    await updateNotificationAnalytics(organizationId, 'notification_delivered', {
      notificationId,
      recipientId,
      deliveryResults,
      deliveredAt: event.eventTime,
      correlationId
    });

    // Update user engagement metrics
    await updateUserEngagementMetrics(recipientId, 'notification_received', organizationId);

    logger.info('Notification delivered event processed successfully', {
      notificationId,
      recipientId,
      organizationId,
      correlationId
    });

  } catch (error) {
    logger.error('Error handling notification delivered event', {
      notificationId,
      recipientId,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function handleNotificationFailedEvent(event: any, correlationId: string): Promise<void> {
  const { notificationId, recipientId, organizationId, error, retryCount } = event.data;

  logger.info('Notification failed event received', {
    notificationId,
    recipientId,
    organizationId,
    error,
    retryCount,
    correlationId
  });

  try {
    // Update notification analytics
    await updateNotificationAnalytics(organizationId, 'notification_failed', {
      notificationId,
      recipientId,
      error,
      retryCount,
      failedAt: event.eventTime,
      correlationId
    });

    // If retry count is below threshold, queue for retry
    if (retryCount < 3) {
      const success = await serviceBusService.sendToQueue('notification-delivery', {
        body: {
          notificationId,
          recipientId,
          organizationId,
          retryCount: retryCount + 1,
          correlationId
        },
        correlationId,
        messageId: `notification-retry-${notificationId}-${retryCount + 1}-${Date.now()}`
      });

      if (!success) {
        logger.error('Failed to queue notification retry', { notificationId, retryCount, correlationId });
      }
    }

    logger.info('Notification failed event processed successfully', {
      notificationId,
      recipientId,
      organizationId,
      retryCount,
      correlationId
    });

  } catch (error) {
    logger.error('Error handling notification failed event', {
      notificationId,
      recipientId,
      organizationId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function updateNotificationAnalytics(organizationId: string, event: string, data: any): Promise<void> {
  try {
    const analyticsData = {
      id: uuidv4(),
      organizationId,
      event,
      data,
      timestamp: new Date().toISOString(),
      partitionKey: organizationId
    };

    await db.createItem('notification-analytics', analyticsData);

    // Publish analytics event
    await publishEvent(
      EventType.ANALYTICS_GENERATED,
      `organizations/${organizationId}/notification-analytics/${event}`,
      analyticsData
    );
  } catch (error) {
    logger.error('Error updating notification analytics', {
      organizationId,
      event,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Missing function implementations for workflow management
 */
async function updateWorkflowExecutionStatus(executionId: string, status: string, metadata: any): Promise<void> {
  try {
    const execution = await db.readItem('workflow-executions', executionId, metadata.organizationId || 'default');
    if (execution) {
      await db.updateItem('workflow-executions', {
        ...execution,
        status,
        lastUpdated: new Date().toISOString(),
        ...metadata
      });

      logger.info('Workflow execution status updated', { executionId, status });
    }
  } catch (error) {
    logger.error('Error updating workflow execution status', {
      executionId,
      status,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function notifyWorkflowParticipants(workflowId: string, executionId: string, event: string, data: any): Promise<void> {
  try {
    // Get workflow participants
    const workflow = await db.readItem('workflow-templates', workflowId, data.organizationId);
    if (workflow && workflow.participants) {
      for (const participant of workflow.participants) {
        const success = await serviceBusService.sendToQueue('notification-delivery', {
          body: {
            notificationId: uuidv4(),
            recipientId: participant.userId,
            organizationId: data.organizationId,
            channels: ['in_app', 'email'],
            priority: 'normal',
            content: {
              title: `Workflow ${event}`,
              message: `Workflow ${workflowId} has ${event}`,
              data: { workflowId, executionId, event }
            }
          },
          correlationId: data.correlationId,
          messageId: `workflow-notification-${participant.userId}-${Date.now()}`
        });

        if (!success) {
          logger.error('Failed to queue workflow participant notification', {
            workflowId,
            executionId,
            participantId: participant.userId
          });
        }
      }
    }
  } catch (error) {
    logger.error('Error notifying workflow participants', {
      workflowId,
      executionId,
      event,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function updateWorkflowAnalytics(organizationId: string, event: string, data: any): Promise<void> {
  try {
    const analyticsData = {
      id: uuidv4(),
      organizationId,
      event,
      data,
      timestamp: new Date().toISOString(),
      partitionKey: organizationId
    };

    await db.createItem('workflow-analytics', analyticsData);

    // Publish analytics event
    await publishEvent(
      EventType.ANALYTICS_GENERATED,
      `organizations/${organizationId}/workflow-analytics/${event}`,
      analyticsData
    );
  } catch (error) {
    logger.error('Error updating workflow analytics', {
      organizationId,
      event,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function cacheWorkflowState(executionId: string, state: any): Promise<void> {
  try {
    const cacheKey = `workflow:${executionId}:state`;
    await redis.setex(cacheKey, 3600, JSON.stringify(state)); // Cache for 1 hour

    logger.debug('Workflow state cached', { executionId });
  } catch (error) {
    logger.error('Error caching workflow state', {
      executionId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Missing event handler implementations
 */
async function handleDocumentUploadedEvent(event: any, correlationId: string): Promise<void> {
  const { documentId, organizationId, userId, metadata } = event.data;

  logger.info('Document uploaded event received', { documentId, organizationId, userId, correlationId });

  try {
    // Update document status
    await db.updateItem('documents', {
      id: documentId,
      status: 'uploaded',
      uploadedAt: new Date().toISOString(),
      correlationId,
      partitionKey: organizationId
    });

    // Trigger document processing workflow
    await publishEvent(
      EventType.DOCUMENT_UPLOAD_COMPLETED,
      `documents/${documentId}/upload-completed`,
      { documentId, organizationId, userId, metadata, correlationId }
    );
  } catch (error) {
    logger.error('Error handling document uploaded event', {
      documentId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function handleDocumentProcessingCompletedEvent(event: any, correlationId: string): Promise<void> {
  const { documentId, processingResults, organizationId } = event.data;

  logger.info('Document processing completed event received', { documentId, correlationId });

  try {
    // Update document with processing results
    const document = await db.readItem('documents', documentId, organizationId);
    if (document) {
      await db.updateItem('documents', {
        id: document.id,
        ...document,
        status: 'processed',
        processingResults,
        processedAt: new Date().toISOString(),
        correlationId
      });
    }
  } catch (error) {
    logger.error('Error handling document processing completed event', {
      documentId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function handleDocumentProcessingFailedEvent(event: any, correlationId: string): Promise<void> {
  const { documentId, error: processingError, organizationId } = event.data;

  logger.error('Document processing failed event received', { documentId, processingError, correlationId });

  try {
    // Update document status to failed
    const document = await db.readItem('documents', documentId, organizationId);
    if (document) {
      await db.updateItem('documents', {
        id: document.id,
        ...document,
        status: 'processing_failed',
        processingError,
        failedAt: new Date().toISOString(),
        correlationId
      });
    }

    // Route for manual review
    await routeForManualReview(documentId, document?.createdBy || 'system', {
      reason: 'processing_failed',
      error: processingError,
      organizationId,
      correlationId
    });
  } catch (error) {
    logger.error('Error handling document processing failed event', {
      documentId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function handleAIOperationCompletedEvent(event: any, correlationId: string): Promise<void> {
  const { operationId, documentId, results, organizationId } = event.data;

  logger.info('AI operation completed event received', { operationId, documentId, correlationId });

  try {
    // Update document with AI results
    const document = await db.readItem('documents', documentId, organizationId);
    if (document) {
      await db.updateItem('documents', {
        id: document.id,
        ...document,
        aiResults: results,
        aiProcessedAt: new Date().toISOString(),
        correlationId
      });
    }

    // Update analytics
    await updateUserAnalytics(organizationId, 'ai_operation_completed', {
      operationId,
      documentId,
      results,
      correlationId
    });
  } catch (error) {
    logger.error('Error handling AI operation completed event', {
      operationId,
      documentId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function handleAIOperationFailedEvent(event: any, correlationId: string): Promise<void> {
  const { operationId, documentId, error: operationError, organizationId } = event.data;

  logger.error('AI operation failed event received', { operationId, documentId, operationError, correlationId });

  try {
    // Log the failure
    await db.createItem('ai-operation-failures', {
      id: uuidv4(),
      operationId,
      documentId,
      error: operationError,
      timestamp: new Date().toISOString(),
      correlationId,
      partitionKey: organizationId
    });

    // Notify administrators if critical
    if (operationError?.severity === 'critical') {
      await notifySystemAdministrators('ai_operation_failed', { operationId, documentId, error: operationError }, correlationId);
    }
  } catch (error) {
    logger.error('Error handling AI operation failed event', {
      operationId,
      documentId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function handleWorkflowStepCompletedEvent(event: any, correlationId: string): Promise<void> {
  const { workflowId, executionId, stepId, stepResult, organizationId } = event.data;

  logger.info('Workflow step completed event received', { workflowId, executionId, stepId, correlationId });

  try {
    // Update workflow execution
    const execution = await db.readItem('workflow-executions', executionId, organizationId);
    if (execution) {
      const completedSteps = execution.completedSteps || [];
      completedSteps.push(stepId);

      await db.updateItem('workflow-executions', {
        id: execution.id,
        ...execution,
        completedSteps,
        lastCompletedStep: stepId,
        lastStepResult: stepResult,
        lastUpdated: new Date().toISOString(),
        correlationId
      });
    }

    // Update analytics
    await updateWorkflowAnalytics(organizationId, 'step_completed', {
      workflowId,
      executionId,
      stepId,
      stepResult,
      correlationId
    });
  } catch (error) {
    logger.error('Error handling workflow step completed event', {
      workflowId,
      executionId,
      stepId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function handleWorkflowFailedEvent(event: any, correlationId: string): Promise<void> {
  const { workflowId, executionId, error: workflowError, organizationId } = event.data;

  logger.error('Workflow failed event received', { workflowId, executionId, workflowError, correlationId });

  try {
    // Update workflow execution status
    const execution = await db.readItem('workflow-executions', executionId, organizationId);
    if (execution) {
      await db.updateItem('workflow-executions', {
        id: execution.id,
        ...execution,
        status: 'failed',
        error: workflowError,
        failedAt: new Date().toISOString(),
        correlationId
      });
    }

    // Notify workflow participants
    await notifyWorkflowParticipants(workflowId, executionId, 'failed', {
      organizationId,
      error: workflowError,
      correlationId
    });
  } catch (error) {
    logger.error('Error handling workflow failed event', {
      workflowId,
      executionId,
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Route document for manual review
 */
async function routeForManualReview(documentId: string, userId: string, reviewData: any): Promise<void> {
  try {
    // Create manual review record
    const reviewRecord = {
      id: uuidv4(),
      documentId,
      userId,
      reason: reviewData.reason,
      error: reviewData.error,
      organizationId: reviewData.organizationId,
      status: 'pending_review',
      createdAt: new Date().toISOString(),
      correlationId: reviewData.correlationId,
      partitionKey: reviewData.organizationId
    };

    await db.createItem('manual-reviews', reviewRecord);

    // Send notification to review team
    const success = await serviceBusService.sendToQueue('notification-delivery', {
      body: {
        notificationId: uuidv4(),
        recipientId: 'review-team',
        organizationId: reviewData.organizationId,
        channels: ['in_app', 'email'],
        priority: 'high',
        content: {
          title: 'Document Requires Manual Review',
          message: `Document ${documentId} requires manual review due to ${reviewData.reason}`,
          data: { documentId, reviewData }
        }
      },
      correlationId: reviewData.correlationId,
      messageId: `manual-review-${documentId}-${Date.now()}`
    });

    if (!success) {
      logger.error('Failed to queue manual review notification', { documentId, reason: reviewData.reason });
    }

    logger.info('Document routed for manual review', { documentId, reason: reviewData.reason });
  } catch (error) {
    logger.error('Error routing document for manual review', {
      documentId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Export metrics for monitoring
 */
export function getEventGridMetrics(): EventGridMetrics {
  return { ...metrics };
}

/**
 * Reset metrics (for testing or periodic reset)
 */
export function resetEventGridMetrics(): void {
  metrics = {
    eventsReceived: 0,
    eventsProcessed: 0,
    eventsFailed: 0,
    eventsReplayed: 0,
    averageProcessingTime: 0,
    lastProcessedTime: new Date(),
    eventsByType: new Map(),
    failuresByType: new Map()
  };
}

// Export the publish function for use by other modules
export { publishEvent, EventType, validateEventGridEvent, processEventGridEventEnhanced };

// Register HTTP functions
app.http('event-grid-webhook', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'anonymous', // Event Grid needs anonymous access
  route: 'eventgrid/webhook',
  handler: handleEventGridWebhook
});

app.http('event-grid-publish', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'eventgrid/publish',
  handler: publishCustomEvent
});

// Register native Event Grid triggers
app.eventGrid('storage-events-trigger', {
  handler: handleStorageEventGridTrigger
});

app.eventGrid('custom-events-trigger', {
  handler: handleCustomEventGridTrigger
});
