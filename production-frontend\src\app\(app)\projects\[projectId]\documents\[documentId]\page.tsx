'use client'

/**
 * Unified Document Workspace - Production Ready
 * Integrated AI chat, collaboration, editing, workflows, and document management
 */

import React, { useEffect, useState, useRef, useCallback } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import { useToast } from '@/components/ui/use-toast'
import {
  Brain,
  Send,
  MessageSquare,
  Users,
  FileText,
  Edit3,
  Share2,
  Download,
  Settings,
  Sparkles,
  Zap,
  Bot,
  User,
  Paperclip,
  Search,
  History,
  Workflow,
  PenTool,
  Eye,
  Copy,
  RefreshCw,
  Loader2,
  ChevronRight,
  ChevronLeft,
  Maximize2,
  Minimize2
} from 'lucide-react'

// Components
import { DocumentViewer } from '@/components/documents/document-viewer'
import { DocumentMetadata } from '@/components/documents/document-metadata'
import { DocumentVersions } from '@/components/documents/document-versions'
import { DocumentComments } from '@/components/documents/document-comments'
import { EditorJSRichTextEditor } from '@/components/documents/editorjs-rich-text-editor'

// Hooks and Services
import { useDocuments } from '@/hooks/documents/useDocuments'
import { useProjects } from '@/hooks/projects/useProjects'
import { useRAGQuery } from '@/hooks/ai/useRAG'
// import { useChatCompletion } from '@/hooks/ai/useChatCompletion'
import {
  useUser,
  useStartAIOperation,
  useCurrentSession,
  useSessionParticipants,
  useCreateCollaborationSession,
  useJoinCollaborationSession,
  useSendMessage,
  useCollaborationMessages,
  useUpdateCursor,
  useUpdatePresence
} from '@/stores'
import { cn } from '@/lib/utils'

// Types for the unified workspace
interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: {
    model?: string
    tokensUsed?: number
    confidence?: number
    sources?: Array<{
      title: string
      excerpt: string
      relevance: number
    }>
    reasoning?: string
  }
}

interface WorkspaceMode {
  view: 'viewer' | 'editor'
  aiPanel: boolean
  collaborationPanel: boolean
  workflowPanel: boolean
  fullscreen: boolean
}

export default function UnifiedDocumentWorkspace() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()

  // Route parameters
  const projectId = params?.projectId as string
  const documentId = params?.documentId as string

  // State management
  const [workspaceMode, setWorkspaceMode] = useState<WorkspaceMode>({
    view: 'viewer',
    aiPanel: true,
    collaborationPanel: false,
    workflowPanel: false,
    fullscreen: false
  })
  const [activeTab, setActiveTab] = useState('document')
  const [aiMessages, setAiMessages] = useState<ChatMessage[]>([])
  const [aiInput, setAiInput] = useState('')
  const [isAiTyping, setIsAiTyping] = useState(false)
  const [selectedAiModel, setSelectedAiModel] = useState<'deepseek-r1' | 'llama'>('deepseek-r1')
  const [aiSettings, setAiSettings] = useState({
    useRAG: true,
    enableReasoning: true,
    temperature: 0.7,
    maxTokens: 4000
  })

  // Refs
  const aiMessagesEndRef = useRef<HTMLDivElement>(null)
  const aiInputRef = useRef<HTMLTextAreaElement>(null)

  // Store hooks
  const user = useUser()
  const startAIOperation = useStartAIOperation()
  const currentSession = useCurrentSession()
  const sessionParticipants = useSessionParticipants()
  const createCollaborationSession = useCreateCollaborationSession()
  const joinCollaborationSession = useJoinCollaborationSession()
  const sendMessage = useSendMessage()
  const collaborationMessages = useCollaborationMessages()
  const updateCursor = useUpdateCursor()
  const updatePresence = useUpdatePresence()

  // Data hooks
  const { projects } = useProjects({ organizationId: user?.organizationId })
  const { documents: documentsResponse } = useDocuments({ projectId })
  const ragQuery = useRAGQuery()
  // const chatCompletion = useChatCompletion() // Commented out since hook doesn't exist yet

  // Derived data
  const project = projects?.find((p: any) => p.id === projectId)
  const document = documentsResponse?.find((d: any) => d.id === documentId)
  const isLoading = false
  const error = null

  // Auto-scroll AI messages
  const scrollAiToBottom = useCallback(() => {
    aiMessagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  useEffect(() => {
    scrollAiToBottom()
  }, [aiMessages, scrollAiToBottom])

  // Initialize collaboration session when document loads
  useEffect(() => {
    if (document && user && !currentSession) {
      // Auto-create collaboration session for the document
      createCollaborationSession({
        documentId: document.id,
        name: `Collaboration on ${document.name}`,
        description: 'Real-time collaboration session',
        maxParticipants: 10
      }).catch(console.error)
    }
  }, [document, user, currentSession, createCollaborationSession])

  // Update presence when user is active
  useEffect(() => {
    if (currentSession) {
      updatePresence(true)

      const handleVisibilityChange = () => {
        updatePresence(!globalThis.document?.hidden)
      }

      if (typeof window !== 'undefined') {
        globalThis.document?.addEventListener('visibilitychange', handleVisibilityChange)
        return () => globalThis.document?.removeEventListener('visibilitychange', handleVisibilityChange)
      }
    }
  }, [currentSession, updatePresence])

  // Error handling
  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to load document',
        variant: 'destructive',
      })
    }
  }, [error, toast])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading document workspace...</p>
        </div>
      </div>
    )
  }

  if (!document) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">Document not found</h3>
          <p className="text-muted-foreground mb-4">
            The document you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button onClick={() => router.push(`/projects/${projectId}/documents`)}>
            Back to Documents
          </Button>
        </div>
      </div>
    )
  }

  // AI Chat Functions
  const sendAiMessage = async () => {
    if (!aiInput.trim() || !user?.organizationId) return

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      role: 'user',
      content: aiInput.trim(),
      timestamp: new Date().toISOString()
    }

    setAiMessages(prev => [...prev, userMessage])
    setAiInput('')
    setIsAiTyping(true)

    try {
      let aiResponse: any

      if (aiSettings.useRAG) {
        // Use RAG with document context
        aiResponse = await ragQuery.mutateAsync({
          query: aiInput.trim(),
          organizationId: user.organizationId,
          projectId: projectId,
          options: {
            maxResults: 5,
            minRelevance: 0.7,
            includeReasoning: aiSettings.enableReasoning,
            useAdvancedAI: selectedAiModel === 'deepseek-r1',
            temperature: aiSettings.temperature,
            maxTokens: aiSettings.maxTokens,
            contextWindow: 4000
          },
          filters: {
            documentIds: [documentId] // Focus on current document
          }
        })
      } else {
        // Direct AI completion with document context
        aiResponse = await startAIOperation({
          type: 'CONTENT_GENERATION',
          parameters: {
            prompt: `Document Context: ${document.name}\nProject: ${project?.name}\n\nUser Query: ${aiInput.trim()}`,
            model: selectedAiModel,
            temperature: aiSettings.temperature,
            maxTokens: aiSettings.maxTokens,
            systemPrompt: `You are an AI assistant helping with document "${document.name}" in project "${project?.name}". Provide helpful, accurate responses based on the document context.`,
            documentContext: {
              documentId: document.id,
              documentName: document.name,
              projectId: projectId,
              projectName: project?.name
            }
          },
          organizationId: user.organizationId,
          documentId: documentId,
          projectId: projectId,
          priority: 'NORMAL'
        })
      }

      const assistantMessage: ChatMessage = {
        id: `msg_${Date.now()}_ai`,
        role: 'assistant',
        content: aiResponse.results?.content || aiResponse.answer || 'I apologize, but I encountered an issue generating a response.',
        timestamp: new Date().toISOString(),
        metadata: {
          model: selectedAiModel,
          tokensUsed: aiResponse.tokensUsed || aiResponse.results?.tokensUsed,
          confidence: aiResponse.confidence || aiResponse.results?.confidence,
          sources: aiResponse.sources,
          reasoning: aiResponse.reasoning || aiResponse.results?.reasoning
        }
      }

      setAiMessages(prev => [...prev, assistantMessage])

    } catch (error: any) {
      const errorMessage: ChatMessage = {
        id: `msg_${Date.now()}_error`,
        role: 'assistant',
        content: `I apologize, but I encountered an error: ${error.message || 'Unknown error'}. Please try again.`,
        timestamp: new Date().toISOString()
      }

      setAiMessages(prev => [...prev, errorMessage])

      toast({
        title: 'AI Error',
        description: error.message || 'Failed to get AI response',
        variant: 'destructive'
      })
    } finally {
      setIsAiTyping(false)
    }
  }

  const handleAiKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendAiMessage()
    }
  }

  // Workspace mode toggles
  const toggleAiPanel = () => {
    setWorkspaceMode(prev => ({ ...prev, aiPanel: !prev.aiPanel }))
  }

  const toggleCollaborationPanel = () => {
    setWorkspaceMode(prev => ({ ...prev, collaborationPanel: !prev.collaborationPanel }))
  }

  const toggleWorkflowPanel = () => {
    setWorkspaceMode(prev => ({ ...prev, workflowPanel: !prev.workflowPanel }))
  }

  const toggleViewMode = () => {
    setWorkspaceMode(prev => ({
      ...prev,
      view: prev.view === 'viewer' ? 'editor' : 'viewer'
    }))
  }

  const toggleFullscreen = () => {
    setWorkspaceMode(prev => ({ ...prev, fullscreen: !prev.fullscreen }))
  }

  return (
    <div className={cn(
      "h-screen flex flex-col",
      workspaceMode.fullscreen && "fixed inset-0 z-50 bg-background"
    )}>
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-4">
            <div>
              <h1 className="text-lg font-semibold truncate max-w-[300px]">
                {document.name}
              </h1>
              <p className="text-sm text-muted-foreground">
                {project?.name} • {sessionParticipants.length} collaborator{sessionParticipants.length !== 1 ? 's' : ''}
              </p>
            </div>

            {/* Document status badges */}
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{document.status}</Badge>
              {currentSession && (
                <Badge variant="default">
                  <Users className="h-3 w-3 mr-1" />
                  Live
                </Badge>
              )}
              {workspaceMode.view === 'editor' && (
                <Badge variant="secondary">
                  <Edit3 className="h-3 w-3 mr-1" />
                  Editing
                </Badge>
              )}
            </div>
          </div>

          {/* Header actions */}
          <div className="flex items-center space-x-2">
            <Button
              variant={workspaceMode.view === 'editor' ? 'default' : 'outline'}
              size="sm"
              onClick={toggleViewMode}
            >
              {workspaceMode.view === 'editor' ? (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </>
              ) : (
                <>
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit
                </>
              )}
            </Button>

            <Button
              variant={workspaceMode.aiPanel ? 'default' : 'outline'}
              size="sm"
              onClick={toggleAiPanel}
            >
              <Brain className="h-4 w-4 mr-2" />
              AI
            </Button>

            <Button
              variant={workspaceMode.collaborationPanel ? 'default' : 'outline'}
              size="sm"
              onClick={toggleCollaborationPanel}
            >
              <Users className="h-4 w-4 mr-2" />
              Collaborate
            </Button>

            <Button
              variant={workspaceMode.workflowPanel ? 'default' : 'outline'}
              size="sm"
              onClick={toggleWorkflowPanel}
            >
              <Workflow className="h-4 w-4 mr-2" />
              Workflow
            </Button>

            <Separator orientation="vertical" className="h-6" />

            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>

            <Button variant="outline" size="sm">
              <Download className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
            >
              {workspaceMode.fullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Main workspace */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal">
          {/* Main document area */}
          <ResizablePanel defaultSize={workspaceMode.aiPanel || workspaceMode.collaborationPanel ? 60 : 100}>
            <div className="h-full flex flex-col">
              {/* Document tabs */}
              <div className="border-b">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="h-10 p-0 bg-transparent border-0">
                    <TabsTrigger value="document" className="data-[state=active]:bg-background data-[state=active]:shadow-sm">
                      <FileText className="h-4 w-4 mr-2" />
                      Document
                    </TabsTrigger>
                    <TabsTrigger value="metadata" className="data-[state=active]:bg-background data-[state=active]:shadow-sm">
                      Metadata
                    </TabsTrigger>
                    <TabsTrigger value="versions" className="data-[state=active]:bg-background data-[state=active]:shadow-sm">
                      <History className="h-4 w-4 mr-2" />
                      Versions
                    </TabsTrigger>
                    <TabsTrigger value="comments" className="data-[state=active]:bg-background data-[state=active]:shadow-sm">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Comments
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              {/* Document content */}
              <div className="flex-1 overflow-hidden">
                <Tabs value={activeTab} className="h-full">
                  <TabsContent value="document" className="h-full m-0 p-0">
                    <div className="h-full">
                      {workspaceMode.view === 'editor' ? (
                        <EditorJSRichTextEditor
                          documentId={documentId}
                          projectId={projectId}
                          organizationId={user?.organizationId || ''}
                          documentName={document.name}
                          initialContent={''}
                          currentUser={user ? {
                            id: user.id,
                            name: user.displayName || user.firstName || 'User',
                            avatarUrl: user.avatar
                          } : {
                            id: 'anonymous',
                            name: 'Anonymous User'
                          }}
                          readOnly={false}
                          autoSaveInterval={30000}
                          onSaved={() => {
                            toast({
                              title: 'Document saved',
                              description: 'Your changes have been saved successfully.',
                            })
                          }}
                        />
                      ) : (
                        <DocumentViewer document={document} />
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="metadata" className="h-full m-0 p-4">
                    <DocumentMetadata document={document} />
                  </TabsContent>

                  <TabsContent value="versions" className="h-full m-0 p-4">
                    <DocumentVersions documentId={documentId} />
                  </TabsContent>

                  <TabsContent value="comments" className="h-full m-0 p-4">
                    <DocumentComments documentId={documentId} />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </ResizablePanel>

          {/* AI Panel */}
          {workspaceMode.aiPanel && (
            <>
              <ResizableHandle />
              <ResizablePanel defaultSize={40} minSize={30} maxSize={60}>
                <div className="h-full flex flex-col border-l">
                  {/* AI Panel Header */}
                  <div className="border-b p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold flex items-center">
                        <Brain className="h-5 w-5 mr-2" />
                        AI Assistant
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleAiPanel}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* AI Model Selection */}
                    <div className="flex gap-2">
                      <Button
                        variant={selectedAiModel === 'deepseek-r1' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedAiModel('deepseek-r1')}
                        className="flex-1"
                      >
                        <Brain className="h-3 w-3 mr-1" />
                        DeepSeek R1
                      </Button>
                      <Button
                        variant={selectedAiModel === 'llama' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedAiModel('llama')}
                        className="flex-1"
                      >
                        <Zap className="h-3 w-3 mr-1" />
                        Llama 3.3
                      </Button>
                    </div>

                    {/* AI Settings */}
                    <div className="flex items-center gap-4 mt-3 text-xs">
                      <div className="flex items-center gap-1">
                        <span>RAG:</span>
                        <Button
                          variant={aiSettings.useRAG ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setAiSettings(prev => ({ ...prev, useRAG: !prev.useRAG }))}
                          className="h-5 px-2 text-xs"
                        >
                          {aiSettings.useRAG ? 'On' : 'Off'}
                        </Button>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>Reasoning:</span>
                        <Button
                          variant={aiSettings.enableReasoning ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setAiSettings(prev => ({ ...prev, enableReasoning: !prev.enableReasoning }))}
                          className="h-5 px-2 text-xs"
                        >
                          {aiSettings.enableReasoning ? 'On' : 'Off'}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* AI Chat Messages */}
                  <ScrollArea className="flex-1 p-4">
                    <div className="space-y-4">
                      {aiMessages.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                          <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p className="text-sm">Ask me anything about this document!</p>
                          <p className="text-xs mt-1">I have access to the document content and project knowledge.</p>
                        </div>
                      )}

                      {aiMessages.map((message) => (
                        <div
                          key={message.id}
                          className={cn(
                            "flex gap-3",
                            message.role === 'user' ? "justify-end" : "justify-start"
                          )}
                        >
                          <div
                            className={cn(
                              "max-w-[85%] rounded-lg p-3 text-sm",
                              message.role === 'user'
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted"
                            )}
                          >
                            <div className="flex items-start gap-2">
                              {message.role === 'assistant' && (
                                <Bot className="h-4 w-4 mt-0.5 flex-shrink-0" />
                              )}
                              {message.role === 'user' && (
                                <User className="h-4 w-4 mt-0.5 flex-shrink-0" />
                              )}
                              <div className="flex-1">
                                <div className="prose prose-sm max-w-none">
                                  {message.content}
                                </div>

                                {message.metadata?.sources && (
                                  <div className="mt-2 pt-2 border-t border-border/50">
                                    <div className="text-xs font-medium mb-1">Sources:</div>
                                    <div className="space-y-1">
                                      {message.metadata.sources.map((source, idx) => (
                                        <div key={idx} className="text-xs bg-background/50 rounded p-1">
                                          <div className="font-medium">{source.title}</div>
                                          <div className="opacity-70 truncate">{source.excerpt}</div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}

                                {message.metadata?.reasoning && (
                                  <div className="mt-2 pt-2 border-t border-border/50">
                                    <div className="text-xs font-medium mb-1">Reasoning:</div>
                                    <div className="text-xs opacity-70">{message.metadata.reasoning}</div>
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className="flex items-center justify-between mt-2 text-xs opacity-50">
                              <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                              {message.metadata && (
                                <div className="flex items-center gap-2">
                                  {message.metadata.model && (
                                    <span>{message.metadata.model}</span>
                                  )}
                                  {message.metadata.confidence && (
                                    <span>{Math.round(message.metadata.confidence * 100)}%</span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}

                      {isAiTyping && (
                        <div className="flex gap-3 justify-start">
                          <div className="bg-muted rounded-lg p-3 max-w-[85%]">
                            <div className="flex items-center gap-2">
                              <Bot className="h-4 w-4" />
                              <div className="flex items-center gap-1">
                                <div className="w-1.5 h-1.5 bg-current rounded-full animate-bounce" />
                                <div className="w-1.5 h-1.5 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                                <div className="w-1.5 h-1.5 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                    <div ref={aiMessagesEndRef} />
                  </ScrollArea>

                  {/* AI Input */}
                  <div className="border-t p-4">
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <Textarea
                          ref={aiInputRef}
                          value={aiInput}
                          onChange={(e) => setAiInput(e.target.value)}
                          onKeyPress={handleAiKeyPress}
                          placeholder="Ask about this document..."
                          className="min-h-[60px] resize-none text-sm"
                          disabled={isAiTyping}
                        />
                      </div>
                      <div className="flex flex-col gap-2">
                        <Button
                          onClick={sendAiMessage}
                          disabled={!aiInput.trim() || isAiTyping}
                          size="sm"
                        >
                          {isAiTyping ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Send className="h-4 w-4" />
                          )}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Paperclip className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-3">
                        <span>Model: {selectedAiModel}</span>
                        <span>Context: Document + Project</span>
                      </div>
                      <span>{aiInput.length} chars</span>
                    </div>
                  </div>
                </div>
              </ResizablePanel>
            </>
          )}

          {/* Collaboration Panel */}
          {workspaceMode.collaborationPanel && (
            <>
              <ResizableHandle />
              <ResizablePanel defaultSize={30} minSize={25} maxSize={50}>
                <div className="h-full flex flex-col border-l">
                  {/* Collaboration Header */}
                  <div className="border-b p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold flex items-center">
                        <Users className="h-5 w-5 mr-2" />
                        Collaboration
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleCollaborationPanel}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Session Status */}
                    {currentSession && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Session Status</span>
                          <Badge variant="default">Active</Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span>Participants</span>
                          <span>{sessionParticipants.length}</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Participants List */}
                  <div className="border-b p-4">
                    <h4 className="font-medium text-sm mb-3">Active Participants</h4>
                    <div className="space-y-2">
                      {sessionParticipants.map((participant) => (
                        <div key={participant.userId} className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <User className="h-4 w-4" />
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium">{participant.userId}</div>
                            <div className="text-xs text-muted-foreground">{participant.role}</div>
                          </div>
                          <div className="w-2 h-2 rounded-full bg-green-500" />
                        </div>
                      ))}

                      {sessionParticipants.length === 0 && (
                        <div className="text-center py-4 text-muted-foreground text-sm">
                          No active participants
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Collaboration Messages */}
                  <ScrollArea className="flex-1 p-4">
                    <div className="space-y-3">
                      {collaborationMessages.map((message) => (
                        <div key={message.id} className="text-sm">
                          <div className="flex items-start gap-2">
                            <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center flex-shrink-0">
                              <User className="h-3 w-3" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium">{message.userName}</span>
                                <span className="text-xs text-muted-foreground">
                                  {new Date(message.timestamp).toLocaleTimeString()}
                                </span>
                              </div>
                              <div className="text-muted-foreground">{message.content}</div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {collaborationMessages.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                          <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No messages yet</p>
                          <p className="text-xs">Start collaborating!</p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>

                  {/* Collaboration Input */}
                  <div className="border-t p-4">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Send a message..."
                        className="flex-1"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            const input = e.target as HTMLInputElement
                            if (input.value.trim()) {
                              sendMessage(input.value.trim())
                              input.value = ''
                            }
                          }
                        }}
                      />
                      <Button size="sm">
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </ResizablePanel>
            </>
          )}

          {/* Workflow Panel */}
          {workspaceMode.workflowPanel && (
            <>
              <ResizableHandle />
              <ResizablePanel defaultSize={30} minSize={25} maxSize={50}>
                <div className="h-full flex flex-col border-l">
                  <div className="border-b p-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold flex items-center">
                        <Workflow className="h-5 w-5 mr-2" />
                        Workflow
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleWorkflowPanel}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex-1 p-4">
                    <div className="text-center py-8 text-muted-foreground">
                      <Workflow className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-sm">Workflow features</p>
                      <p className="text-xs">Coming soon...</p>
                    </div>
                  </div>
                </div>
              </ResizablePanel>
            </>
          )}
        </ResizablePanelGroup>
      </div>
    </div>
  )
}
