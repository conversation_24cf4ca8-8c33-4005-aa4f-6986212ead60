"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";

function ErrorContent() {
  const searchParams = useSearchParams();
  const [errorMessage, setErrorMessage] = useState<string>("");

  useEffect(() => {
    if (!searchParams) return;

    const error = searchParams.get("error");

    // Map error codes to user-friendly messages
    switch (error) {
      case "Configuration":
        setErrorMessage("There is a problem with the server configuration. Please contact support.");
        break;
      case "AccessDenied":
        setErrorMessage("You do not have permission to sign in.");
        break;
      case "Verification":
        setErrorMessage("The verification link is no longer valid. It may have expired or already been used.");
        break;
      case "OAuthSignin":
      case "OAuthCallback":
      case "OAuthCreateAccount":
      case "EmailCreateAccount":
      case "Callback":
      case "OAuthAccountNotLinked":
      case "EmailSignin":
      case "CredentialsSignin":
        setErrorMessage("There was a problem with your sign-in attempt. Please try again.");
        break;
      case "SessionRequired":
        setErrorMessage("You need to be signed in to access this page.");
        break;
      default:
        setErrorMessage("An unexpected error occurred. Please try again later.");
        break;
    }
  }, [searchParams]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh]">
      <div className="flex items-center justify-center mb-6 text-destructive">
        <AlertCircle size={48} />
      </div>
      <h1 className="text-2xl font-bold mb-4">Authentication Error</h1>
      <p className="text-muted-foreground text-center max-w-md mb-8">{errorMessage}</p>
      <div className="flex gap-4">
        <Button asChild variant="outline">
          <Link href="/login">Try Again</Link>
        </Button>
        <Button asChild>
          <Link href="/">Go to Home</Link>
        </Button>
      </div>
    </div>
  );
}

export default function ErrorPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ErrorContent />
    </Suspense>
  );
}
