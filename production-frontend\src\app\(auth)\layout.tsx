"use client";

import Link from "next/link";
import Image from "next/image";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();

  // Check if user is already authenticated
  useEffect(() => {
    // This will be handled by Next-Auth middleware and session checks
    // No need to manually check localStorage anymore
  }, []);

  return (
    <div className="min-h-screen grid grid-cols-1 md:grid-cols-2">
      {/* Left side - Auth form */}
      <div className="flex flex-col justify-center items-center p-8">
        <div className="w-full max-w-md">
          <div className="mb-8 text-center">
            <Link href="/" className="inline-block">
              <h1 className="text-3xl font-bold">hepz</h1>
              <p className="text-sm text-muted-foreground mt-1">
                Enterprise Document Processing Platform
              </p>
            </Link>
          </div>
          {children}
        </div>
      </div>

      {/* Right side - Image/Branding (hidden on mobile) */}
      <div className="hidden md:flex flex-col justify-center items-center bg-primary/5 p-8">
        <div className="max-w-md text-center">
          <h2 className="text-2xl font-bold mb-4">
            AI-Powered Document Processing
          </h2>
          <p className="text-muted-foreground mb-8">
            Extract insights, analyze content, and collaborate on documents with
            our enterprise-grade platform.
          </p>
          <div className="relative w-full h-80">
            <Image
              src="/images/document-processing.svg"
              alt="Document Processing Illustration"
              fill
              className="object-contain"
              priority
            />
          </div>
          <div className="mt-8 grid grid-cols-3 gap-4">
            <div className="text-center">
              <h3 className="font-medium">AI Analysis</h3>
              <p className="text-sm text-muted-foreground">
                Extract data automatically
              </p>
            </div>
            <div className="text-center">
              <h3 className="font-medium">Collaboration</h3>
              <p className="text-sm text-muted-foreground">
                Work together seamlessly
              </p>
            </div>
            <div className="text-center">
              <h3 className="font-medium">Security</h3>
              <p className="text-sm text-muted-foreground">
                Enterprise-grade protection
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
