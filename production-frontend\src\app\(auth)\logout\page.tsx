"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";

export default function LogoutPage() {
  const router = useRouter();
  const { logout } = useAuth();

  useEffect(() => {
    const performLogout = async () => {
      try {
        // Logout with redirect to login page
        await logout("/login");
      } catch (error) {
        console.error("Error during logout:", error);
        // Redirect to login page even if there's an error
        router.push("/login");
      }
    };

    performLogout();
  }, [router, logout]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh]">
      <h1 className="text-2xl font-bold mb-4">Signing out...</h1>
      <p className="text-muted-foreground">Please wait while we sign you out.</p>
    </div>
  );
}
