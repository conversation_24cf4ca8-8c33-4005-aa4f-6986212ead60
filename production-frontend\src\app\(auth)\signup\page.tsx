"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SubscriptionPlanSelector } from "@/components/auth/subscription-plan-selector";
import { useAuth } from "@/hooks/useAuth";
import { AlertCircle, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function SignupPage() {
  const { error: authError } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const displayError = error || authError;

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Link href="/login">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Login
          </Button>
        </Link>
      </div>

      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Create Your Account</h1>
        <p className="text-muted-foreground">
          Choose a plan and get started with hepz in minutes
        </p>
      </div>

      {displayError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{displayError}</AlertDescription>
        </Alert>
      )}

      <SubscriptionPlanSelector
        showFreeOption={true}
        className="max-w-4xl mx-auto"
      />



      <div className="text-center text-sm text-muted-foreground space-y-2">
        <p>
          By creating an account, you agree to our{" "}
          <Link href="/terms" className="underline hover:text-primary">
            Terms of Service
          </Link>{" "}
          and{" "}
          <Link href="/privacy" className="underline hover:text-primary">
            Privacy Policy
          </Link>
        </p>

        <p>
          This application uses Azure AD B2C for secure authentication.
          Your credentials are managed by Microsoft's identity platform.
        </p>
      </div>

      <div className="text-center text-sm">
        <p className="text-muted-foreground">
          Already have an account?{" "}
          <Link href="/login" className="font-medium text-primary hover:underline">
            Sign in
          </Link>
        </p>
      </div>
    </div>
  );
}
