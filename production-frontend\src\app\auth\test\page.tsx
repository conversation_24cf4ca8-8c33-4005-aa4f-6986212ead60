'use client'

/**
 * MSAL Configuration Test Page
 * Simple test to verify MSAL setup and Azure AD B2C configuration
 */

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle2, AlertCircle, Info } from 'lucide-react'

export default function MSALTestPage() {
  const [testResults, setTestResults] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const runConfigurationTest = async () => {
    setIsLoading(true)
    setTestResults(null)

    try {
      // Test environment variables
      const envVars = {
        clientId: process.env.NEXT_PUBLIC_AZURE_AD_B2C_CLIENT_ID,
        tenantName: process.env.NEXT_PUBLIC_AZURE_AD_B2C_TENANT_NAME,
        authorityDomain: process.env.NEXT_PUBLIC_AZURE_AD_B2C_AUTHORITY_DOMAIN,
        redirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI,
        policies: {
          signIn: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SIGNIN_POLICY,
          signUp: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SIGNUP_POLICY,
          susi: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SUSI_POLICY,
          passwordReset: process.env.NEXT_PUBLIC_AZURE_AD_B2C_PASSWORD_RESET_POLICY,
          profileEdit: process.env.NEXT_PUBLIC_AZURE_AD_B2C_PROFILE_EDIT_POLICY,
        }
      }

      // Test MSAL import
      let msalImportTest = false
      try {
        const { PublicClientApplication } = await import('@azure/msal-browser')
        msalImportTest = !!PublicClientApplication
      } catch (error) {
        console.error('MSAL import failed:', error)
      }

      // Test authority URLs
      const authorityUrls = {
        signIn: `https://${envVars.authorityDomain}/${envVars.tenantName}.onmicrosoft.com/${envVars.policies.signIn}`,
        signUp: `https://${envVars.authorityDomain}/${envVars.tenantName}.onmicrosoft.com/${envVars.policies.signUp}`,
        susi: `https://${envVars.authorityDomain}/${envVars.tenantName}.onmicrosoft.com/${envVars.policies.susi}`,
        passwordReset: `https://${envVars.authorityDomain}/${envVars.tenantName}.onmicrosoft.com/${envVars.policies.passwordReset}`,
        profileEdit: `https://${envVars.authorityDomain}/${envVars.tenantName}.onmicrosoft.com/${envVars.policies.profileEdit}`,
      }

      // Validation checks
      const validations = {
        hasClientId: !!envVars.clientId,
        hasTenantName: !!envVars.tenantName,
        hasAuthorityDomain: !!envVars.authorityDomain,
        hasRedirectUri: !!envVars.redirectUri,
        hasAllPolicies: Object.values(envVars.policies).every(policy => !!policy),
        msalImportWorks: msalImportTest,
        redirectUriMatchesCurrentHost: envVars.redirectUri?.includes(window.location.host),
      }

      setTestResults({
        envVars,
        authorityUrls,
        validations,
        currentHost: window.location.host,
        currentUrl: window.location.href,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      setTestResults({
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getValidationIcon = (isValid: boolean) => {
    return isValid ? (
      <CheckCircle2 className="h-4 w-4 text-green-500" />
    ) : (
      <AlertCircle className="h-4 w-4 text-red-500" />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">MSAL Configuration Test</h1>
          <p className="mt-2 text-gray-600">Verify your Azure AD B2C and MSAL setup</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Configuration Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={runConfigurationTest} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Running Tests...' : 'Run Configuration Test'}
            </Button>
          </CardContent>
        </Card>

        {testResults && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {testResults.error ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{testResults.error}</AlertDescription>
                </Alert>
              ) : (
                <>
                  {/* Validation Results */}
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Validation Results</h3>
                    <div className="space-y-2">
                      {Object.entries(testResults.validations).map(([key, isValid]) => (
                        <div key={key} className="flex items-center space-x-2">
                          {getValidationIcon(isValid as boolean)}
                          <span className="text-sm">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Environment Variables */}
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Environment Variables</h3>
                    <div className="bg-gray-100 p-3 rounded text-sm">
                      <pre>{JSON.stringify(testResults.envVars, null, 2)}</pre>
                    </div>
                  </div>

                  {/* Authority URLs */}
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Authority URLs</h3>
                    <div className="space-y-1 text-sm">
                      {Object.entries(testResults.authorityUrls).map(([key, url]) => (
                        <div key={key}>
                          <strong>{key}:</strong> <code className="bg-gray-100 px-1 rounded">{url as string}</code>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Current Environment */}
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Current Environment</h3>
                    <div className="text-sm space-y-1">
                      <div><strong>Host:</strong> {testResults.currentHost}</div>
                      <div><strong>URL:</strong> {testResults.currentUrl}</div>
                      <div><strong>Timestamp:</strong> {testResults.timestamp}</div>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        )}

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Azure Portal Configuration Required:</strong><br />
            1. Set application type to "Single-page application (SPA)"<br />
            2. Add redirect URI: <code>{process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI}</code><br />
            3. Enable "Allow public client flows"<br />
            4. Enable "Access tokens" and "ID tokens" in implicit grant settings
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
