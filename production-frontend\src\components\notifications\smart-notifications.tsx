'use client'

import React, { useState, useEffect } from 'react'
import { Bell, X, Check, AlertCircle, Info, CheckCircle, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { realtimeService } from '@/services/realtime-service'
import { aiService } from '@/services/ai-service'
import { cn } from '@/lib/utils'

export interface SmartNotification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error' | 'ai' | 'collaboration'
  title: string
  message: string
  timestamp: number
  read: boolean
  actionable?: boolean
  action?: {
    label: string
    onClick: () => void
  }
  metadata?: {
    documentId?: string
    projectId?: string
    userId?: string
    operationId?: string
  }
}

export function SmartNotifications() {
  const [notifications, setNotifications] = useState<SmartNotification[]>([])
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    // Load persisted notifications
    loadNotifications()

    // Set up real-time listeners
    const handleCollaborationEvent = (data: any) => {
      addNotification({
        type: 'collaboration',
        title: 'Collaboration Update',
        message: `${data.event.userName} ${getCollaborationMessage(data.type)}`,
        actionable: true,
        action: {
          label: 'View Document',
          onClick: () => {
            if (data.event.documentId) {
              window.location.href = `/documents/${data.event.documentId}`
            }
          }
        },
        metadata: {
          documentId: data.event.documentId,
          userId: data.event.userId
        }
      })
    }

    const handleSystemMessage = (data: any) => {
      addNotification({
        type: 'info',
        title: 'System Update',
        message: data.message || 'System notification received',
        metadata: data.metadata
      })
    }

    const handleNotification = (data: any) => {
      addNotification({
        type: data.type || 'info',
        title: data.title || 'Notification',
        message: data.message || 'You have a new notification',
        actionable: !!data.action,
        action: data.action,
        metadata: data.metadata
      })
    }

    // Subscribe to real-time events
    realtimeService.on('collaborationEvent', handleCollaborationEvent)
    realtimeService.on('systemMessage', handleSystemMessage)
    realtimeService.on('notification', handleNotification)

    // Check for AI operation updates periodically
    const aiCheckInterval = setInterval(checkAIOperations, 30000) // Every 30 seconds

    return () => {
      realtimeService.off('collaborationEvent', handleCollaborationEvent)
      realtimeService.off('systemMessage', handleSystemMessage)
      realtimeService.off('notification', handleNotification)
      clearInterval(aiCheckInterval)
    }
  }, [])

  const addNotification = (notification: Omit<SmartNotification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: SmartNotification = {
      ...notification,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      read: false
    }

    setNotifications(prev => {
      const updated = [newNotification, ...prev].slice(0, 50) // Keep only last 50
      saveNotifications(updated)
      return updated
    })
  }

  const markAsRead = (id: string) => {
    setNotifications(prev => {
      const updated = prev.map(n => n.id === id ? { ...n, read: true } : n)
      saveNotifications(updated)
      return updated
    })
  }

  const markAllAsRead = () => {
    setNotifications(prev => {
      const updated = prev.map(n => ({ ...n, read: true }))
      saveNotifications(updated)
      return updated
    })
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => {
      const updated = prev.filter(n => n.id !== id)
      saveNotifications(updated)
      return updated
    })
  }

  const clearAll = () => {
    setNotifications([])
    saveNotifications([])
  }

  const checkAIOperations = async () => {
    try {
      const operations = aiService.getActiveOperations()
      
      // Check for completed operations that might need notification
      operations.forEach(operation => {
        if (operation.status === 'completed' || operation.status === 'failed') {
          const existingNotification = notifications.find(n => 
            n.metadata?.operationId === operation.id
          )
          
          if (!existingNotification) {
            addNotification({
              type: operation.status === 'completed' ? 'success' : 'error',
              title: `AI Operation ${operation.status === 'completed' ? 'Completed' : 'Failed'}`,
              message: `${operation.type} operation has ${operation.status}`,
              actionable: operation.status === 'completed',
              action: operation.status === 'completed' ? {
                label: 'View Results',
                onClick: () => {
                  // Navigate to results or show details
                  console.log('View AI operation results:', operation.id)
                }
              } : undefined,
              metadata: {
                operationId: operation.id
              }
            })
          }
        }
      })
    } catch (error) {
      console.error('Failed to check AI operations:', error)
    }
  }

  const getCollaborationMessage = (type: string): string => {
    switch (type) {
      case 'edit': return 'made changes to a document'
      case 'comment': return 'added a comment'
      case 'presence': return 'joined the collaboration'
      default: return 'performed an action'
    }
  }

  const getNotificationIcon = (type: SmartNotification['type']) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'error': return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'ai': return <Info className="h-4 w-4 text-blue-600" />
      case 'collaboration': return <Info className="h-4 w-4 text-purple-600" />
      default: return <Info className="h-4 w-4 text-blue-600" />
    }
  }

  const formatTimeAgo = (timestamp: number): string => {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    if (minutes > 0) return `${minutes}m ago`
    return 'Just now'
  }

  const unreadCount = notifications.filter(n => !n.read).length

  const loadNotifications = () => {
    try {
      const stored = localStorage.getItem('smart_notifications')
      if (stored) {
        setNotifications(JSON.parse(stored))
      }
    } catch (error) {
      console.warn('Failed to load notifications:', error)
    }
  }

  const saveNotifications = (notifications: SmartNotification[]) => {
    try {
      localStorage.setItem('smart_notifications', JSON.stringify(notifications))
    } catch (error) {
      console.warn('Failed to save notifications:', error)
    }
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0" align="end">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Notifications</CardTitle>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={markAllAsRead}
                    className="h-7 text-xs"
                  >
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAll}
                  className="h-7 text-xs"
                >
                  Clear all
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <Separator />
          
          <CardContent className="p-0">
            <ScrollArea className="h-96">
              {notifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Bell className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
                  <p className="text-sm text-muted-foreground">No notifications yet</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    You'll see updates about your documents and collaborations here
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={cn(
                        "p-4 hover:bg-muted/50 transition-colors",
                        !notification.read && "bg-blue-50/50"
                      )}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <h4 className={cn(
                              "text-sm font-medium",
                              !notification.read && "font-semibold"
                            )}>
                              {notification.title}
                            </h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeNotification(notification.id)}
                              className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mt-1">
                            {notification.message}
                          </p>
                          
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-muted-foreground">
                              {formatTimeAgo(notification.timestamp)}
                            </span>
                            
                            <div className="flex items-center gap-2">
                              {!notification.read && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => markAsRead(notification.id)}
                                  className="h-6 text-xs"
                                >
                                  <Check className="h-3 w-3 mr-1" />
                                  Mark read
                                </Button>
                              )}
                              
                              {notification.actionable && notification.action && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    notification.action?.onClick()
                                    markAsRead(notification.id)
                                    setIsOpen(false)
                                  }}
                                  className="h-6 text-xs"
                                >
                                  {notification.action.label}
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  )
}
