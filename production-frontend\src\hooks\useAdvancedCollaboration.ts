/**
 * Advanced Collaboration Hook
 * Provides real-time collaboration features, presence tracking, and AI-powered insights
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { realtimeService, CollaborationSession, CollaborationParticipant, CollaborationInsight, DocumentLock } from '@/services/realtime-service'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/hooks/useAuth'

interface CollaborationState {
  currentSession: CollaborationSession | null
  participants: CollaborationParticipant[]
  insights: CollaborationInsight[]
  documentLocks: Map<string, DocumentLock>
  isConnected: boolean
  isLoading: boolean
  error: string | null
}

interface CollaborationActions {
  createSession: (sessionData: any) => Promise<CollaborationSession | null>
  joinSession: (sessionId: string) => Promise<CollaborationSession | null>
  leaveSession: () => Promise<void>
  lockDocument: (documentId: string, type?: 'full' | 'section' | 'element', scope?: any, reason?: string) => Promise<DocumentLock | null>
  unlockDocument: (documentId: string) => Promise<void>
  updatePresence: (presence: any) => Promise<void>
  getInsights: () => Promise<void>
  sendMessage: (message: string) => Promise<void>
  shareScreen: () => Promise<void>
}

export function useAdvancedCollaboration(documentId?: string): CollaborationState & CollaborationActions {
  const [state, setState] = useState<CollaborationState>({
    currentSession: null,
    participants: [],
    insights: [],
    documentLocks: new Map(),
    isConnected: false,
    isLoading: false,
    error: null
  })

  const { user } = useAuth()
  const { toast } = useToast()
  const presenceUpdateTimer = useRef<NodeJS.Timeout>()
  const lastPresenceUpdate = useRef<any>()

  // Initialize collaboration
  useEffect(() => {
    if (user) {
      realtimeService.setCurrentUser(user.id, user.displayName || user.email)
    }

    // Set up event listeners
    const handleSessionJoined = (session: CollaborationSession) => {
      setState(prev => ({
        ...prev,
        currentSession: session,
        participants: session.participants
      }))
      
      toast({
        title: 'Collaboration Started',
        description: `Joined collaboration session: ${session.title}`
      })
    }

    const handlePresenceUpdate = (participants: CollaborationParticipant[]) => {
      setState(prev => ({
        ...prev,
        participants
      }))
    }

    const handleDocumentLocked = (lock: DocumentLock) => {
      setState(prev => {
        const newLocks = new Map(prev.documentLocks)
        newLocks.set(`${lock.documentId}:${lock.type}`, lock)
        return {
          ...prev,
          documentLocks: newLocks
        }
      })

      if (lock.userId !== user?.id) {
        toast({
          title: 'Document Locked',
          description: `${lock.userName} has locked the document for editing`
        })
      }
    }

    const handleDocumentUnlocked = (data: { documentId: string }) => {
      setState(prev => {
        const newLocks = new Map(prev.documentLocks)
        // Remove all locks for this document
        for (const [key] of newLocks) {
          if (key.startsWith(data.documentId)) {
            newLocks.delete(key)
          }
        }
        return {
          ...prev,
          documentLocks: newLocks
        }
      })

      toast({
        title: 'Document Unlocked',
        description: 'Document is now available for editing'
      })
    }

    const handleConnectionChange = (connected: boolean) => {
      setState(prev => ({
        ...prev,
        isConnected: connected
      }))
    }

    // Register event listeners
    realtimeService.on('sessionJoined', handleSessionJoined)
    realtimeService.on('presenceUpdated', handlePresenceUpdate)
    realtimeService.on('documentLocked', handleDocumentLocked)
    realtimeService.on('documentUnlocked', handleDocumentUnlocked)
    realtimeService.on('connected', () => handleConnectionChange(true))
    realtimeService.on('disconnected', () => handleConnectionChange(false))

    // Set initial connection state
    setState(prev => ({
      ...prev,
      isConnected: realtimeService.getConnectionStatus()
    }))

    // Cleanup
    return () => {
      realtimeService.off('sessionJoined', handleSessionJoined)
      realtimeService.off('presenceUpdated', handlePresenceUpdate)
      realtimeService.off('documentLocked', handleDocumentLocked)
      realtimeService.off('documentUnlocked', handleDocumentUnlocked)
      realtimeService.off('connected', () => handleConnectionChange(true))
      realtimeService.off('disconnected', () => handleConnectionChange(false))
      
      if (presenceUpdateTimer.current) {
        clearInterval(presenceUpdateTimer.current)
      }
    }
  }, [user, toast])

  // Create collaboration session
  const createSession = useCallback(async (sessionData: any): Promise<CollaborationSession | null> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const session = await realtimeService.createCollaborationSession(sessionData)
      
      if (session) {
        setState(prev => ({
          ...prev,
          currentSession: session,
          participants: session.participants,
          isLoading: false
        }))
        
        toast({
          title: 'Session Created',
          description: `Collaboration session "${session.title}" has been created`
        })
      }
      
      return session
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create session'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }))
      
      toast({
        title: 'Error',
        description: 'Failed to create collaboration session',
        variant: 'destructive'
      })
      
      return null
    }
  }, [toast])

  // Join collaboration session
  const joinSession = useCallback(async (sessionId: string): Promise<CollaborationSession | null> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const session = await realtimeService.joinCollaborationSession(sessionId)
      
      if (session) {
        setState(prev => ({
          ...prev,
          currentSession: session,
          participants: session.participants,
          isLoading: false
        }))
      }
      
      return session
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to join session'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }))
      
      toast({
        title: 'Error',
        description: 'Failed to join collaboration session',
        variant: 'destructive'
      })
      
      return null
    }
  }, [toast])

  // Leave collaboration session
  const leaveSession = useCallback(async (): Promise<void> => {
    if (state.currentSession && documentId) {
      try {
        await realtimeService.leaveDocument(documentId)
        
        setState(prev => ({
          ...prev,
          currentSession: null,
          participants: [],
          insights: []
        }))
        
        if (presenceUpdateTimer.current) {
          clearInterval(presenceUpdateTimer.current)
        }
        
        toast({
          title: 'Left Session',
          description: 'You have left the collaboration session'
        })
      } catch (error) {
        console.error('Failed to leave session:', error)
      }
    }
  }, [state.currentSession, documentId, toast])

  // Lock document
  const lockDocument = useCallback(async (
    docId: string,
    type: 'full' | 'section' | 'element' = 'full',
    scope?: any,
    reason?: string
  ): Promise<DocumentLock | null> => {
    if (!state.currentSession) {
      toast({
        title: 'Error',
        description: 'No active collaboration session',
        variant: 'destructive'
      })
      return null
    }

    try {
      const lock = await realtimeService.lockDocumentAdvanced(
        docId,
        state.currentSession.id,
        type,
        scope,
        reason
      )
      
      if (lock) {
        toast({
          title: 'Document Locked',
          description: 'You have locked the document for editing'
        })
      }
      
      return lock
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to lock document',
        variant: 'destructive'
      })
      return null
    }
  }, [state.currentSession, toast])

  // Unlock document
  const unlockDocument = useCallback(async (docId: string): Promise<void> => {
    try {
      await realtimeService.unlockDocument(docId)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to unlock document',
        variant: 'destructive'
      })
    }
  }, [toast])

  // Update presence
  const updatePresence = useCallback(async (presence: any): Promise<void> => {
    if (!state.currentSession) return

    // Throttle presence updates to avoid spam
    const now = Date.now()
    if (lastPresenceUpdate.current && now - lastPresenceUpdate.current < 100) {
      return
    }
    lastPresenceUpdate.current = now

    try {
      await realtimeService.updatePresenceAdvanced(state.currentSession.id, presence)
    } catch (error) {
      console.warn('Failed to update presence:', error)
    }
  }, [state.currentSession])

  // Get collaboration insights
  const getInsights = useCallback(async (): Promise<void> => {
    if (!state.currentSession) return

    try {
      const insights = await realtimeService.getCollaborationInsights(state.currentSession.id)
      setState(prev => ({
        ...prev,
        insights
      }))
    } catch (error) {
      console.error('Failed to get collaboration insights:', error)
    }
  }, [state.currentSession])

  // Send message (placeholder for future chat feature)
  const sendMessage = useCallback(async (message: string): Promise<void> => {
    // TODO: Implement chat messaging
    console.log('Sending message:', message)
  }, [])

  // Share screen (placeholder for future screen sharing)
  const shareScreen = useCallback(async (): Promise<void> => {
    // TODO: Implement screen sharing
    console.log('Starting screen share')
  }, [])

  // Auto-join document if documentId is provided
  useEffect(() => {
    if (documentId && state.isConnected) {
      realtimeService.joinDocument(documentId)
    }
  }, [documentId, state.isConnected])

  // Auto-load insights periodically
  useEffect(() => {
    if (state.currentSession) {
      getInsights()
      
      const interval = setInterval(getInsights, 60000) // Every minute
      return () => clearInterval(interval)
    }
  }, [state.currentSession, getInsights])

  return {
    ...state,
    createSession,
    joinSession,
    leaveSession,
    lockDocument,
    unlockDocument,
    updatePresence,
    getInsights,
    sendMessage,
    shareScreen
  }
}

// Hook for presence tracking
export function usePresenceTracking(sessionId?: string) {
  const [cursor, setCursor] = useState<{ line: number; column: number } | null>(null)
  const [selection, setSelection] = useState<any>(null)
  const [viewport, setViewport] = useState<{ top: number; bottom: number } | null>(null)
  const { updatePresence } = useAdvancedCollaboration()

  // Update presence when cursor/selection changes
  useEffect(() => {
    if (sessionId) {
      updatePresence({
        cursor,
        selection,
        viewport
      })
    }
  }, [cursor, selection, viewport, sessionId, updatePresence])

  return {
    cursor,
    selection,
    viewport,
    setCursor,
    setSelection,
    setViewport
  }
}
