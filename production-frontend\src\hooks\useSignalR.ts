/**
 * React Hooks for SignalR Integration
 * Production-ready hooks for real-time features
 */

import { useEffect, useRef, useCallback, useState } from 'react'
import { HubConnectionState } from '@microsoft/signalr'
import { signalRClient, SignalREventName, SignalREventHandler } from '@/lib/signalr/signalr-client'
import type { Document, UserContext } from '@/types/backend'

/**
 * Base SignalR connection hook
 */
export function useSignalR() {
  const [connectionState, setConnectionState] = useState<HubConnectionState | null>(
    signalRClient.connectionState
  )
  const [isConnected, setIsConnected] = useState(signalRClient.isConnected)
  const [connectionId, setConnectionId] = useState<string | null>(signalRClient.connectionId)

  useEffect(() => {
    const updateConnectionState = () => {
      setConnectionState(signalRClient.connectionState)
      setIsConnected(signalRClient.isConnected)
      setConnectionId(signalRClient.connectionId)
    }

    // Set up event listeners
    signalRClient.on('Connected', updateConnectionState)
    signalRClient.on('Disconnected', updateConnectionState)
    signalRClient.on('Reconnecting', updateConnectionState)
    signalRClient.on('Reconnected', updateConnectionState)

    // Initial state update
    updateConnectionState()

    return () => {
      signalRClient.off('Connected', updateConnectionState)
      signalRClient.off('Disconnected', updateConnectionState)
      signalRClient.off('Reconnecting', updateConnectionState)
      signalRClient.off('Reconnected', updateConnectionState)
    }
  }, [])

  const connect = useCallback(async () => {
    try {
      await signalRClient.connect()
    } catch (error) {
      console.error('Failed to connect to SignalR:', error)
    }
  }, [])

  const disconnect = useCallback(async () => {
    try {
      await signalRClient.disconnect()
    } catch (error) {
      console.error('Failed to disconnect from SignalR:', error)
    }
  }, [])

  const subscribe = useCallback((event: string, handler: (...args: any[]) => void) => {
    signalRClient.on(event as SignalREventName, handler as SignalREventHandler<SignalREventName>)
  }, [])

  const unsubscribe = useCallback((event: string, handler: (...args: any[]) => void) => {
    signalRClient.off(event as SignalREventName, handler as SignalREventHandler<SignalREventName>)
  }, [])

  const invoke = useCallback(async (method: string, ...args: any[]) => {
    try {
      return await signalRClient.invoke(method, ...args)
    } catch (error) {
      console.error(`Failed to invoke SignalR method ${method}:`, error)
      throw error
    }
  }, [])

  return {
    connectionState,
    isConnected,
    connectionId,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    invoke,
  }
}

/**
 * Hook for subscribing to SignalR events
 */
export function useSignalREvent<T extends SignalREventName>(
  event: T,
  handler: SignalREventHandler<T>,
  deps: React.DependencyList = []
) {
  const handlerRef = useRef(handler)
  handlerRef.current = handler

  useEffect(() => {
    const stableHandler = (...args: any[]) => {
      ;(handlerRef.current as any)(...args)
    }

    signalRClient.on(event, stableHandler as SignalREventHandler<T>)

    return () => {
      signalRClient.off(event, stableHandler as SignalREventHandler<T>)
    }
  }, [event, ...deps])
}

/**
 * Hook for document real-time updates
 */
export function useDocumentRealTime(documentId?: string) {
  const [document, setDocument] = useState<Document | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState<{
    operationId: string
    status: 'started' | 'completed' | 'failed'
    result?: any
    error?: string
  } | null>(null)

  // Document updated event
  useSignalREvent('DocumentUpdated', useCallback((updatedDocument: Document) => {
    if (!documentId || updatedDocument.id === documentId) {
      setDocument(updatedDocument)
    }
  }, [documentId]))

  // Document deleted event
  useSignalREvent('DocumentDeleted', useCallback((deletedDocumentId: string) => {
    if (documentId === deletedDocumentId) {
      setDocument(null)
    }
  }, [documentId]))

  // Processing events
  useSignalREvent('DocumentProcessingStarted', useCallback((docId: string, operationId: string) => {
    if (documentId === docId) {
      setIsProcessing(true)
      setProcessingProgress({ operationId, status: 'started' })
    }
  }, [documentId]))

  useSignalREvent('DocumentProcessingCompleted', useCallback((docId: string, operationId: string, result: any) => {
    if (documentId === docId) {
      setIsProcessing(false)
      setProcessingProgress({ operationId, status: 'completed', result })
    }
  }, [documentId]))

  useSignalREvent('DocumentProcessingFailed', useCallback((docId: string, operationId: string, error: string) => {
    if (documentId === docId) {
      setIsProcessing(false)
      setProcessingProgress({ operationId, status: 'failed', error })
    }
  }, [documentId]))

  return {
    document,
    isProcessing,
    processingProgress,
  }
}

/**
 * Hook for collaboration session management
 */
export function useCollaboration(sessionId?: string) {
  const [participants, setParticipants] = useState<UserContext[]>([])
  const [cursors, setCursors] = useState<Map<string, { x: number; y: number; page?: number }>>(new Map())
  const [documentLocks, setDocumentLocks] = useState<Map<string, { userId: string; lockId: string }>>(new Map())
  const [comments, setComments] = useState<any[]>([])

  // User joined/left events
  useSignalREvent('UserJoined', useCallback((sessId: string, user: UserContext) => {
    if (sessionId === sessId) {
      setParticipants(prev => [...prev.filter(p => p.id !== user.id), user])
    }
  }, [sessionId]))

  useSignalREvent('UserLeft', useCallback((sessId: string, userId: string) => {
    if (sessionId === sessId) {
      setParticipants(prev => prev.filter(p => p.id !== userId))
      setCursors(prev => {
        const newCursors = new Map(prev)
        newCursors.delete(userId)
        return newCursors
      })
    }
  }, [sessionId]))

  // Cursor movement
  useSignalREvent('CursorMoved', useCallback((sessId: string, userId: string, position: { x: number; y: number; page?: number }) => {
    if (sessionId === sessId) {
      setCursors(prev => new Map(prev).set(userId, position))
    }
  }, [sessionId]))

  // Document locking
  useSignalREvent('DocumentLocked', useCallback((documentId: string, userId: string, lockId: string) => {
    setDocumentLocks(prev => new Map(prev).set(documentId, { userId, lockId }))
  }, []))

  useSignalREvent('DocumentUnlocked', useCallback((documentId: string, lockId: string) => {
    setDocumentLocks(prev => {
      const newLocks = new Map(prev)
      const lock = newLocks.get(documentId)
      if (lock?.lockId === lockId) {
        newLocks.delete(documentId)
      }
      return newLocks
    })
  }, []))

  // Comments
  useSignalREvent('CommentAdded', useCallback((_documentId: string, comment: any) => {
    setComments(prev => [...prev, comment])
  }, []))

  useSignalREvent('CommentUpdated', useCallback((_documentId: string, comment: any) => {
    setComments(prev => prev.map(c => c.id === comment.id ? comment : c))
  }, []))

  useSignalREvent('CommentDeleted', useCallback((_documentId: string, commentId: string) => {
    setComments(prev => prev.filter(c => c.id !== commentId))
  }, []))

  // Actions
  const joinSession = useCallback(async () => {
    if (sessionId) {
      await signalRClient.joinCollaborationSession(sessionId)
    }
  }, [sessionId])

  const leaveSession = useCallback(async () => {
    if (sessionId) {
      await signalRClient.leaveCollaborationSession(sessionId)
    }
  }, [sessionId])

  const updateCursor = useCallback(async (position: { x: number; y: number; page?: number }) => {
    if (sessionId) {
      await signalRClient.updateCursor(sessionId, position)
    }
  }, [sessionId])

  const lockDocument = useCallback(async (documentId: string) => {
    return await signalRClient.lockDocument(documentId)
  }, [])

  const unlockDocument = useCallback(async (documentId: string, lockId: string) => {
    await signalRClient.unlockDocument(documentId, lockId)
  }, [])

  const addComment = useCallback(async (documentId: string, comment: any) => {
    await signalRClient.addComment(documentId, comment)
  }, [])

  return {
    participants,
    cursors,
    documentLocks,
    comments,
    joinSession,
    leaveSession,
    updateCursor,
    lockDocument,
    unlockDocument,
    addComment,
  }
}

/**
 * Hook for real-time notifications
 */
export function useNotifications() {
  const [notifications, setNotifications] = useState<any[]>([])
  const [systemAlerts, setSystemAlerts] = useState<any[]>([])

  useSignalREvent('NotificationReceived', useCallback((notification: any) => {
    setNotifications(prev => [notification, ...prev].slice(0, 50)) // Keep last 50 notifications
  }, []))

  useSignalREvent('SystemAlert', useCallback((alert: any) => {
    setSystemAlerts(prev => [alert, ...prev].slice(0, 10)) // Keep last 10 alerts
  }, []))

  const clearNotifications = useCallback(() => {
    setNotifications([])
  }, [])

  const clearSystemAlerts = useCallback(() => {
    setSystemAlerts([])
  }, [])

  const markNotificationAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
    )
  }, [])

  return {
    notifications,
    systemAlerts,
    clearNotifications,
    clearSystemAlerts,
    markNotificationAsRead,
  }
}

/**
 * Hook for presence indicators
 */
export function usePresence() {
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set())
  const { isConnected } = useSignalR()

  useSignalREvent('UserJoined', useCallback((_sessionId: string, user: UserContext) => {
    setOnlineUsers(prev => new Set(prev).add(user.id))
  }, []))

  useSignalREvent('UserLeft', useCallback((_sessionId: string, userId: string) => {
    setOnlineUsers(prev => {
      const newSet = new Set(prev)
      newSet.delete(userId)
      return newSet
    })
  }, []))

  const isUserOnline = useCallback((userId: string) => {
    return onlineUsers.has(userId)
  }, [onlineUsers])

  return {
    onlineUsers: Array.from(onlineUsers),
    isUserOnline,
    isConnected,
  }
}
