/**
 * Audit Service
 * Handles audit logging and security monitoring
 */

import { backendApiClient } from './backend-api-client'

export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error'
}

export enum SecurityAuditEventType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  FAILED_LOGIN = 'FAILED_LOGIN',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  DOCUMENT_ACCESS = 'DOCUMENT_ACCESS',
  DOCUMENT_UPLOAD = 'DOCUMENT_UPLOAD',
  DOCUMENT_DELETE = 'DOCUMENT_DELETE',
  PERMISSION_CHANGE = 'PERMISSION_CHANGE',
  ROLE_CHANGE = 'ROLE_CHANGE',
  ORGANIZATION_ACCESS = 'ORGANIZATION_ACCESS',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  DATA_EXPORT = 'DATA_EXPORT',
  SYSTEM_CONFIG_CHANGE = 'SYSTEM_CONFIG_CHANGE'
}

export interface AuditLog {
  id: string
  userId: string
  userEmail: string
  userName: string
  action: string
  resource: string
  resourceId: string
  details: Record<string, any>
  ipAddress: string
  userAgent: string
  timestamp: string
  organizationId: string
  severity: AuditSeverity
  category: 'authentication' | 'authorization' | 'data' | 'system' | 'security'
  status: 'success' | 'failure' | 'warning'
}

export interface AuditLogQuery {
  userId?: string
  action?: string
  resource?: string
  severity?: string
  category?: string
  status?: string
  startDate?: string
  endDate?: string
  ipAddress?: string
  organizationId?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface AuditLogFilter {
  userId?: string
  userEmail?: string
  action?: string
  eventType?: string
  resource?: string
  severity?: string
  category?: string
  status?: string
  startDate?: string
  endDate?: string
  ipAddress?: string
  organizationId?: string
  page?: number
  pageSize?: number
}

export interface AuditLogResponse {
  logs: AuditLog[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface SecurityMetrics {
  totalEvents: number
  criticalEvents: number
  failedLogins: number
  suspiciousActivity: number
  topActions: Array<{
    action: string
    count: number
  }>
  topUsers: Array<{
    userId: string
    userName: string
    count: number
  }>
  eventsByCategory: Record<string, number>
  eventsBySeverity: Record<string, number>
  timeline: Array<{
    date: string
    count: number
    criticalCount: number
  }>
}

export interface SecurityAlert {
  id: string
  type: 'failed_login' | 'suspicious_activity' | 'data_breach' | 'unauthorized_access'
  severity: AuditSeverity
  title: string
  description: string
  userId?: string
  ipAddress?: string
  timestamp: string
  status: 'open' | 'investigating' | 'resolved' | 'false_positive'
  assignedTo?: string
  resolvedAt?: string
  resolvedBy?: string
  notes?: string
}

class AuditService {
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7071/api'
  }

  /**
   * Get audit logs with filtering and pagination
   */
  async getAuditLogs(query: AuditLogQuery = {}): Promise<AuditLogResponse> {
    try {
      return await backendApiClient.request('/audit/logs', {
        method: 'GET',
        params: query
      })
    } catch (error) {
      console.error('Failed to fetch audit logs:', error)

      // Return empty results for production
      return {
        logs: [],
        total: 0,
        page: query.page || 1,
        pageSize: query.pageSize || 10,
        totalPages: 0
      }
    }
  }

  /**
   * Get security metrics and analytics
   */
  async getSecurityMetrics(period: { start: string; end: string }): Promise<SecurityMetrics> {
    try {
      return await backendApiClient.request('/audit/metrics', {
        method: 'GET',
        params: period
      })
    } catch (error) {
      console.error('Failed to fetch security metrics:', error)

      // Return empty metrics for production
      return {
        totalEvents: 0,
        criticalEvents: 0,
        failedLogins: 0,
        suspiciousActivity: 0,
        topActions: [],
        topUsers: [],
        eventsByCategory: {},
        eventsBySeverity: {},
        timeline: []
      }
    }
  }

  /**
   * Get security alerts
   */
  async getSecurityAlerts(query: { status?: string; severity?: string } = {}): Promise<SecurityAlert[]> {
    try {
      return await backendApiClient.request('/audit/alerts', {
        method: 'GET',
        params: query
      })
    } catch (error) {
      console.error('Failed to fetch security alerts:', error)

      // Return empty alerts for production
      return []
    }
  }

  /**
   * Create audit log entry
   */
  async createAuditLog(log: Omit<AuditLog, 'id' | 'timestamp'>): Promise<void> {
    try {
      await backendApiClient.request('/audit/logs', {
        method: 'POST',
        body: JSON.stringify(log)
      })
    } catch (error) {
      console.error('Failed to create audit log:', error)
    }
  }

  /**
   * Export audit logs
   */
  async exportAuditLogs(query: AuditLogQuery, format: 'csv' | 'json' | 'pdf' = 'csv'): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/audit/export?format=${format}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(query)
      })

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`)
      }

      return await response.blob()
    } catch (error) {
      console.error('Failed to export audit logs:', error)
      throw error
    }
  }

  /**
   * Update security alert status
   */
  async updateSecurityAlert(alertId: string, updates: Partial<SecurityAlert>): Promise<SecurityAlert> {
    try {
      return await backendApiClient.request(`/audit/alerts/${alertId}`, {
        method: 'PATCH',
        body: JSON.stringify(updates)
      })
    } catch (error) {
      console.error('Failed to update security alert:', error)
      throw error
    }
  }


}

export const auditService = new AuditService()
export default auditService
