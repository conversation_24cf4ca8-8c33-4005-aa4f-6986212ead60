/**
 * Backend API Client - Comprehensive Integration with Azure Functions
 * Type-safe client for all backend endpoints
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios'
import { performanceMonitor } from '../lib/performance'
import { memoryCache } from '../lib/cache'
import { retry } from '../lib/utils'
import type {
  StandardToken,
  UserContext,
  AuthResult,
  Document,
  DocumentStatus,
  DocumentType,
  AIOperation,
  CollaborationSession,
  SessionSettings,
  StorageOperation,
  Organization,
  Project,
  ProjectVisibility,
  Workflow,
  WorkflowStatus,
  Template,
  TemplateType,
  TemplateStatus
} from '../types/backend'

// API Configuration
const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7071',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
}

// Error codes
const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
} as const

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  timestamp: string
  requestId?: string
}

export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: string
  requestId?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }
  success: boolean
  timestamp: string
}

class BackendApiClient {
  private instance: AxiosInstance
  private token: string | null = null

  constructor() {
    this.instance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
    this.loadTokenFromStorage()
  }

  private setupInterceptors() {
    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        // Add authorization header
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`
        }

        // Add correlation ID for tracking
        config.headers['x-correlation-id'] = this.generateCorrelationId()
        config.headers['x-request-time'] = new Date().toISOString()

        // Start performance tracking
        ;(config as any).metadata = { startTime: performance.now() }

        return config
      },
      (error) => Promise.reject(this.handleError(error))
    )

    // Response interceptor
    this.instance.interceptors.response.use(
      (response) => {
        // Record performance metrics
        const config = response.config as any
        if (config.metadata?.startTime) {
          const duration = performance.now() - config.metadata.startTime
          const endpoint = config.url?.replace(config.baseURL || '', '') || 'unknown'

          performanceMonitor.recordAPIMetric(
            endpoint,
            config.method?.toUpperCase() || 'GET',
            duration,
            response.status,
            JSON.stringify(response.data).length
          )
        }

        // Cache GET requests if they're successful
        if (config.method?.toLowerCase() === 'get' && response.status === 200) {
          const cacheKey = this.getCacheKey(config.url, config.params)
          memoryCache.set(cacheKey, response.data, 5 * 60 * 1000) // 5 minutes
        }

        return response
      },
      async (error) => {
        // Record error metrics
        const config = error.config as any
        if (config?.metadata?.startTime) {
          const duration = performance.now() - config.metadata.startTime
          const endpoint = config.url?.replace(config.baseURL || '', '') || 'unknown'

          performanceMonitor.recordAPIMetric(
            endpoint,
            config.method?.toUpperCase() || 'GET',
            duration,
            error.response?.status || 0
          )
        }

        const originalRequest = error.config

        // Handle token refresh for 401 errors
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          try {
            await this.refreshToken()
            return this.instance(originalRequest)
          } catch (refreshError) {
            this.clearToken()
            if (typeof window !== 'undefined') {
              window.location.href = '/auth/login'
            }
            return Promise.reject(this.handleError(refreshError))
          }
        }

        return Promise.reject(this.handleError(error))
      }
    )
  }

  private loadTokenFromStorage() {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token')
      if (token) {
        this.setToken(token)
      }
    }
  }

  private generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }

  private getCacheKey(url?: string, params?: any): string {
    const baseKey = url || 'unknown'
    const paramKey = params ? JSON.stringify(params) : ''
    return `${baseKey}${paramKey}`
  }

  private handleError(error: any): ApiError {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<any>

      if (axiosError.response) {
        const { data, status } = axiosError.response
        
        return {
          code: data?.error?.code || this.getErrorCodeFromStatus(status),
          message: data?.error?.message || axiosError.message,
          details: data?.error?.details,
          timestamp: new Date().toISOString(),
          requestId: axiosError.config?.headers?.['x-correlation-id'] as string,
        }
      } else if (axiosError.request) {
        return {
          code: ERROR_CODES.NETWORK_ERROR,
          message: 'Network error. Please check your connection.',
          timestamp: new Date().toISOString(),
        }
      }
    }

    return {
      code: ERROR_CODES.SERVER_ERROR,
      message: error?.message || 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
    }
  }

  private getErrorCodeFromStatus(status: number): string {
    switch (status) {
      case 401: return ERROR_CODES.UNAUTHORIZED
      case 403: return ERROR_CODES.FORBIDDEN
      case 404: return ERROR_CODES.NOT_FOUND
      case 422: return ERROR_CODES.VALIDATION_ERROR
      case 408: return ERROR_CODES.TIMEOUT_ERROR
      default: return ERROR_CODES.SERVER_ERROR
    }
  }

  setToken(token: string | null) {
    this.token = token
    if (typeof window !== 'undefined') {
      if (token) {
        localStorage.setItem('auth_token', token)
      } else {
        localStorage.removeItem('auth_token')
      }
    }
  }

  getToken(): string | null {
    return this.token
  }

  get baseURL(): string {
    return this.instance.defaults.baseURL || ''
  }



  // Auth methods
  auth = {
    login: async (credentials: any) => {
      return await this.request('/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials)
      })
    },

    register: async (data: any) => {
      return await this.request('/auth/register', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },

    logout: async () => {
      return await this.request('/auth/logout', {
        method: 'POST'
      })
    },

    refreshToken: async (refreshToken: string) => {
      return await this.request('/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({ refreshToken })
      })
    },

    getProfile: async () => {
      return await this.request('/auth/profile')
    },

    updateProfile: async (data: any) => {
      return await this.request('/auth/profile', {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    },

    changePassword: async (data: any) => {
      return await this.request('/auth/change-password', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },

    requestPasswordReset: async (email: string) => {
      return await this.request('/auth/request-password-reset', {
        method: 'POST',
        body: JSON.stringify({ email })
      })
    },

    resetPassword: async (data: any) => {
      return await this.request('/auth/reset-password', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },

    verifyEmail: async (token: string) => {
      return await this.request('/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token })
      })
    },

    resendEmailVerification: async () => {
      return await this.request('/auth/resend-verification', {
        method: 'POST'
      })
    }
  }

  clearToken() {
    this.token = null
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
    }
  }

  private async refreshToken(): Promise<void> {
    const refreshToken = typeof window !== 'undefined' 
      ? localStorage.getItem('refresh_token') 
      : null

    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await this.instance.post<ApiResponse<StandardToken>>('/auth/refresh', {
      refreshToken,
    })

    const { accessToken, refreshToken: newRefreshToken } = response.data.data
    
    this.setToken(accessToken)
    if (typeof window !== 'undefined' && newRefreshToken) {
      localStorage.setItem('refresh_token', newRefreshToken)
    }
  }

  // ============================================================================
  // AUTHENTICATION ENDPOINTS
  // ============================================================================

  async login(credentials: { email: string; password: string }): Promise<StandardToken> {
    const response = await this.instance.post<ApiResponse<StandardToken>>('/auth/login', credentials)
    return response.data.data
  }

  async logout(): Promise<void> {
    await this.instance.post('/auth/logout')
    this.clearToken()
  }

  async getProfile(): Promise<UserContext> {
    const response = await this.instance.get<ApiResponse<UserContext>>('/auth/me')
    return response.data.data
  }

  async validateToken(): Promise<AuthResult> {
    const response = await this.instance.get<ApiResponse<AuthResult>>('/auth/validate')
    return response.data.data
  }

  // ============================================================================
  // ORGANIZATION ENDPOINTS
  // ============================================================================

  async getOrganizations(): Promise<Organization[]> {
    const cacheKey = 'organizations'

    // Try cache first
    const cached = memoryCache.get(cacheKey)
    if (cached) {
      return cached
    }

    // Fetch with retry logic
    const response = await retry(
      () => this.instance.get<ApiResponse<Organization[]>>('/organizations'),
      3,
      1000
    )

    const data = response.data.data

    // Cache for 10 minutes
    memoryCache.set(cacheKey, data, 10 * 60 * 1000)

    return data
  }

  async getOrganization(organizationId: string): Promise<Organization> {
    const cacheKey = `organization_${organizationId}`

    // Try cache first
    const cached = memoryCache.get(cacheKey)
    if (cached) {
      return cached
    }

    const response = await retry(
      () => this.instance.get<ApiResponse<Organization>>(`/organizations/${organizationId}`),
      3,
      1000
    )

    const data = response.data.data

    // Cache for 15 minutes
    memoryCache.set(cacheKey, data, 15 * 60 * 1000)

    return data
  }

  async createOrganization(data: any): Promise<Organization> {
    const response = await this.instance.post<ApiResponse<Organization>>('/organizations', data)
    return response.data.data
  }

  async updateOrganization(organizationId: string, data: any): Promise<Organization> {
    const response = await this.instance.put<ApiResponse<Organization>>(`/organizations/${organizationId}`, data)
    return response.data.data
  }

  async deleteOrganization(organizationId: string): Promise<void> {
    await this.instance.delete(`/organizations/${organizationId}`)
  }

  async getOrganizationMembers(organizationId: string): Promise<any[]> {
    const response = await this.instance.get<ApiResponse<any[]>>(`/organizations/${organizationId}/members`)
    return response.data.data
  }

  async inviteOrganizationMember(organizationId: string, inviteData: { email: string; role: string; permissions?: string[] }): Promise<void> {
    await this.instance.post(`/organizations/${organizationId}/members/invite`, inviteData)
  }

  async addOrganizationMember(organizationId: string, data: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>(`/organizations/${organizationId}/members`, data)
    return response.data.data
  }

  async updateOrganizationMemberRole(organizationId: string, userId: string, role: string): Promise<any> {
    const response = await this.instance.put<ApiResponse<any>>(`/organizations/${organizationId}/members/${userId}`, { role })
    return response.data.data
  }

  async removeOrganizationMember(organizationId: string, userId: string): Promise<void> {
    await this.instance.delete(`/organizations/${organizationId}/members/${userId}`)
  }

  async getOrganizationSettings(organizationId: string): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>(`/organizations/${organizationId}/settings`)
    return response.data.data
  }

  async updateOrganizationSettings(organizationId: string, settings: any): Promise<any> {
    const response = await this.instance.put<ApiResponse<any>>(`/organizations/${organizationId}/settings`, settings)
    return response.data.data
  }

  // ============================================================================
  // PROJECT ENDPOINTS
  // ============================================================================

  async getProjects(params?: {
    organizationId?: string
    visibility?: ProjectVisibility[]
    status?: string[]
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<Project>> {
    const cacheKey = `projects_${JSON.stringify(params || {})}`

    // Try cache first
    const cached = memoryCache.get(cacheKey)
    if (cached) {
      return cached
    }

    const response = await retry(
      () => this.instance.get<PaginatedResponse<Project>>('/projects', { params }),
      3,
      1000
    )

    const data = response.data

    // Cache for 5 minutes (shorter for paginated data)
    memoryCache.set(cacheKey, data, 5 * 60 * 1000)

    return data
  }

  async getProject(projectId: string): Promise<Project> {
    const response = await this.instance.get<ApiResponse<Project>>(`/projects/${projectId}`)
    return response.data.data
  }

  async createProject(data: {
    name: string
    description?: string
    visibility: ProjectVisibility
    organizationId: string
    settings?: any
  }): Promise<Project> {
    const response = await this.instance.post<ApiResponse<Project>>('/projects', data)
    return response.data.data
  }

  async updateProject(projectId: string, data: any): Promise<Project> {
    const response = await this.instance.put<ApiResponse<Project>>(`/projects/${projectId}`, data)
    return response.data.data
  }

  async deleteProject(projectId: string): Promise<void> {
    await this.instance.delete(`/projects/${projectId}`)
  }

  async archiveProject(projectId: string): Promise<Project> {
    const response = await this.instance.post<ApiResponse<Project>>(`/projects/${projectId}/archive`)
    return response.data.data
  }

  async restoreProject(projectId: string): Promise<Project> {
    const response = await this.instance.post<ApiResponse<Project>>(`/projects/${projectId}/restore`)
    return response.data.data
  }

  async getProjectMembers(projectId: string): Promise<any[]> {
    const response = await this.instance.get<ApiResponse<any[]>>(`/projects/${projectId}/members`)
    return response.data.data
  }

  async addProjectMember(projectId: string, memberData: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>(`/projects/${projectId}/members`, memberData)
    return response.data.data
  }

  async updateProjectMember(projectId: string, userId: string, updateData: any): Promise<any> {
    const response = await this.instance.put<ApiResponse<any>>(`/projects/${projectId}/members/${userId}`, updateData)
    return response.data.data
  }

  async removeProjectMember(projectId: string, userId: string): Promise<void> {
    await this.instance.delete(`/projects/${projectId}/members/${userId}`)
  }

  async getProjectAnalytics(projectId: string): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>(`/projects/${projectId}/analytics`)
    return response.data.data
  }

  async searchProjects(query: string, filters?: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/projects/search', { query, filters })
    return response.data.data
  }

  async getProjectActivity(projectId: string, params?: any): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>(`/projects/${projectId}/activity`, { params })
    return response.data.data
  }

  async exportProject(projectId: string, format: string): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>(`/projects/${projectId}/export`, { format })
    return response.data.data
  }

  // ============================================================================
  // WORKFLOW ENDPOINTS
  // ============================================================================

  async getWorkflows(params?: {
    organizationId?: string
    projectId?: string
    status?: WorkflowStatus[]
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<Workflow>> {
    const response = await this.instance.get<PaginatedResponse<Workflow>>('/workflows', { params })
    return response.data
  }

  async getWorkflow(workflowId: string): Promise<Workflow> {
    const response = await this.instance.get<ApiResponse<Workflow>>(`/workflows/${workflowId}`)
    return response.data.data
  }

  async createWorkflow(data: {
    name: string
    description?: string
    organizationId: string
    projectId?: string
    steps?: any[]
    settings?: any
  }): Promise<Workflow> {
    const response = await this.instance.post<ApiResponse<Workflow>>('/workflows', data)
    return response.data.data
  }

  async updateWorkflow(workflowId: string, data: {
    name?: string
    description?: string
    steps?: any[]
    settings?: any
  }): Promise<Workflow> {
    const response = await this.instance.put<ApiResponse<Workflow>>(`/workflows/${workflowId}`, data)
    return response.data.data
  }

  async deleteWorkflow(workflowId: string): Promise<void> {
    await this.instance.delete(`/workflows/${workflowId}`)
  }

  async executeWorkflow(workflowId: string, input?: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>(`/workflows/${workflowId}/execute`, { input })
    return response.data.data
  }

  async getWorkflowExecutions(workflowId: string, params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.instance.get<PaginatedResponse<any>>(`/workflows/${workflowId}/executions`, { params })
    return response.data
  }

  async getWorkflowExecution(executionId: string): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>(`/workflow-executions/${executionId}`)
    return response.data.data
  }

  async cancelWorkflowExecution(executionId: string): Promise<void> {
    await this.instance.post(`/workflow-executions/${executionId}/cancel`)
  }

  async retryWorkflowExecution(executionId: string): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>(`/workflow-executions/${executionId}/retry`)
    return response.data.data
  }

  async getWorkflowAnalytics(workflowId: string): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>(`/workflows/${workflowId}/analytics`)
    return response.data.data
  }

  async searchWorkflows(query: string, filters?: any): Promise<PaginatedResponse<Workflow>> {
    const response = await this.instance.get<PaginatedResponse<Workflow>>('/workflows/search', {
      params: { query, ...filters }
    })
    return response.data
  }

  async cloneWorkflow(workflowId: string, name: string): Promise<Workflow> {
    const response = await this.instance.post<ApiResponse<Workflow>>(`/workflows/${workflowId}/clone`, { name })
    return response.data.data
  }

  async exportWorkflow(workflowId: string, format: string): Promise<any> {
    const response = await this.instance.get(`/workflows/${workflowId}/export`, {
      params: { format },
      responseType: 'blob'
    })
    return response.data
  }

  async importWorkflow(file: File, organizationId: string, projectId?: string): Promise<Workflow> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('organizationId', organizationId)
    if (projectId) formData.append('projectId', projectId)

    const response = await this.instance.post<ApiResponse<Workflow>>('/workflows/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    return response.data.data
  }

  async validateWorkflow(definition: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/workflows/validate', { definition })
    return response.data.data
  }

  async testWorkflow(workflowId: string, testInput?: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>(`/workflows/${workflowId}/test`, { testInput })
    return response.data.data
  }

  async getWorkflowTemplates(params?: any): Promise<PaginatedResponse<any>> {
    const response = await this.instance.get<PaginatedResponse<any>>('/workflow-templates', { params })
    return response.data
  }

  async createWorkflowFromTemplate(templateId: string, workflowData: any): Promise<Workflow> {
    const response = await this.instance.post<ApiResponse<Workflow>>(`/workflow-templates/${templateId}/create`, workflowData)
    return response.data.data
  }

  async saveWorkflowAsTemplate(workflowId: string, templateData: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>(`/workflows/${workflowId}/save-as-template`, templateData)
    return response.data.data
  }

  // ============================================================================
  // TEMPLATE ENDPOINTS
  // ============================================================================

  async getTemplates(params?: {
    organizationId?: string
    projectId?: string
    type?: TemplateType[]
    status?: TemplateStatus[]
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<Template>> {
    const response = await this.instance.get<PaginatedResponse<Template>>('/templates', { params })
    return response.data
  }

  async getTemplate(templateId: string): Promise<Template> {
    const response = await this.instance.get<ApiResponse<Template>>(`/templates/${templateId}`)
    return response.data.data
  }

  async createTemplate(data: {
    name: string
    description?: string
    type: TemplateType
    organizationId: string
    projectId?: string
    content?: any
    fields?: any[]
    settings?: any
  }): Promise<Template> {
    const response = await this.instance.post<ApiResponse<Template>>('/templates', data)
    return response.data.data
  }

  async updateTemplate(templateId: string, data: {
    name?: string
    description?: string
    content?: any
    fields?: any[]
    settings?: any
  }): Promise<Template> {
    const response = await this.instance.put<ApiResponse<Template>>(`/templates/${templateId}`, data)
    return response.data.data
  }

  async deleteTemplate(templateId: string): Promise<void> {
    await this.instance.delete(`/templates/${templateId}`)
  }

  // ============================================================================
  // DOCUMENT ENDPOINTS
  // ============================================================================

  async getDocuments(params?: {
    organizationId?: string
    projectId?: string
    status?: DocumentStatus[]
    type?: DocumentType[]
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<Document>> {
    const cacheKey = `documents_${JSON.stringify(params || {})}`

    // Try cache first
    const cached = memoryCache.get(cacheKey)
    if (cached) {
      return cached
    }

    const response = await retry(
      () => this.instance.get<PaginatedResponse<Document>>('/documents', { params }),
      3,
      1000
    )

    const data = response.data

    // Cache for 3 minutes (shorter for frequently changing data)
    memoryCache.set(cacheKey, data, 3 * 60 * 1000)

    return data
  }

  async getDocument(documentId: string): Promise<Document> {
    const response = await this.instance.get<ApiResponse<Document>>(`/documents/${documentId}`)
    return response.data.data
  }

  async uploadDocument(file: File, metadata?: {
    name?: string
    description?: string
    projectId?: string
    tags?: string[]
    autoProcess?: boolean
  }): Promise<Document> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, typeof value === 'string' ? value : JSON.stringify(value))
        }
      })
    }

    const response = await this.instance.post<ApiResponse<Document>>('/documents', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
    return response.data.data
  }

  async updateDocument(documentId: string, updates: Partial<Document>): Promise<Document> {
    const response = await this.instance.patch<ApiResponse<Document>>(`/documents/${documentId}/update`, updates)
    return response.data.data
  }

  async deleteDocument(documentId: string): Promise<void> {
    await this.instance.delete(`/documents/${documentId}`)
  }

  async processDocument(documentId: string, options?: {
    analysisType?: string
    extractTables?: boolean
    extractKeyValuePairs?: boolean
    extractEntities?: boolean
    forceReprocess?: boolean
  }): Promise<AIOperation> {
    const response = await this.instance.post<ApiResponse<AIOperation>>(
      `/documents/${documentId}/process`,
      options
    )
    return response.data.data
  }

  async getDocumentShares(documentId: string): Promise<Array<{
    id: string
    documentId: string
    sharedWith: string[]
    permissions: string[]
    createdAt: string
    expiresAt?: string
  }>> {
    const response = await this.instance.get(`/documents/${documentId}/shares`)
    return response.data
  }

  async shareDocument(data: {
    documentId: string
    emails: string[]
    permissions: string[]
    expiresAt?: string
    message?: string
  }): Promise<{
    shareId: string
    sharedWith: string[]
    permissions: string[]
  }> {
    const response = await this.instance.post(`/documents/${data.documentId}/share`, data)
    return response.data
  }

  async updateDocumentShare(shareId: string, data: {
    permissions?: string[]
    expiresAt?: string
  }): Promise<void> {
    await this.instance.put(`/documents/shares/${shareId}`, data)
  }

  async revokeDocumentShare(shareId: string): Promise<void> {
    await this.instance.delete(`/documents/shares/${shareId}`)
  }

  async generateShareLink(data: {
    documentId: string
    options: {
      expiresAt?: string
      allowDownload?: boolean
      password?: string
    }
  }): Promise<{
    url: string
    expiresAt?: string
    linkId: string
  }> {
    const response = await this.instance.post(`/documents/${data.documentId}/share-link`, data.options)
    return response.data
  }

  // ============================================================================
  // AI/ML ENDPOINTS
  // ============================================================================

  async startAIOperation(params: {
    type: string
    documentId?: string
    parameters?: Record<string, any>
  }): Promise<AIOperation> {
    const response = await this.instance.post<ApiResponse<AIOperation>>('/ai/operations', params)
    return response.data.data
  }

  async getAIOperation(operationId: string): Promise<AIOperation> {
    const response = await this.instance.get<ApiResponse<AIOperation>>(`/ai/operations/${operationId}`)
    return response.data.data
  }

  async listAIOperations(params?: {
    organizationId?: string
    status?: string
    type?: string
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<AIOperation>> {
    const response = await this.instance.get<PaginatedResponse<AIOperation>>('/ai/operations/list', { params })
    return response.data
  }

  async analyzeDocument(documentId: string, options?: {
    analysisType?: string
    extractTables?: boolean
    extractKeyValuePairs?: boolean
  }): Promise<AIOperation> {
    const response = await this.instance.post<ApiResponse<AIOperation>>(
      '/ai/documents/analyze',
      { documentId, ...options }
    )
    return response.data.data
  }

  async cancelAIOperation(operationId: string): Promise<void> {
    await this.instance.post(`/ai/operations/${operationId}/cancel`)
  }

  async retryAIOperation(operationId: string): Promise<AIOperation> {
    const response = await this.instance.post<ApiResponse<AIOperation>>(`/ai/operations/${operationId}/retry`)
    return response.data.data
  }

  async createAIModel(params: {
    name: string
    type: string
    configuration: Record<string, any>
    organizationId: string
  }): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/ai/models', params)
    return response.data.data
  }

  async deployAIModel(modelId: string): Promise<void> {
    await this.instance.post(`/ai/models/${modelId}/deploy`)
  }

  async listAIModels(organizationId: string): Promise<any[]> {
    const response = await this.instance.get<ApiResponse<any[]>>('/ai/models', {
      params: { organizationId }
    })
    return response.data.data
  }

  async processBatch(params: {
    documentIds: string[]
    operationType: string
    organizationId: string
    batchName?: string
  }): Promise<AIOperation> {
    const response = await this.instance.post<ApiResponse<AIOperation>>('/ai/batch/process', params)
    return response.data.data
  }

  // ============================================================================
  // COLLABORATION ENDPOINTS
  // ============================================================================

  async createCollaborationSession(params: {
    documentId: string
    name: string
    description?: string
    maxParticipants?: number
    settings?: Partial<SessionSettings>
  }): Promise<CollaborationSession> {
    const response = await this.instance.post<ApiResponse<CollaborationSession>>(
      '/collaboration/sessions',
      params
    )
    return response.data.data
  }

  async joinCollaborationSession(sessionId: string): Promise<CollaborationSession> {
    const response = await this.instance.post<ApiResponse<CollaborationSession>>(
      `/collaboration/sessions/${sessionId}/join`
    )
    return response.data.data
  }

  async leaveCollaborationSession(sessionId: string): Promise<void> {
    await this.instance.post(`/collaboration/sessions/${sessionId}/leave`)
  }

  async getCollaborationSession(sessionId: string): Promise<CollaborationSession> {
    const response = await this.instance.get<ApiResponse<CollaborationSession>>(
      `/collaboration/sessions/${sessionId}`
    )
    return response.data.data
  }

  async endCollaborationSession(sessionId: string): Promise<void> {
    await this.instance.post(`/collaboration/sessions/${sessionId}/end`)
  }

  async initializeSignalR(): Promise<any> {
    try {
      const { HubConnectionBuilder, LogLevel } = await import('@microsoft/signalr')

      const connection = new HubConnectionBuilder()
        .withUrl(`${API_CONFIG.BASE_URL}/signalr`, {
          accessTokenFactory: () => this.token || ''
        })
        .withAutomaticReconnect({
          nextRetryDelayInMilliseconds: retryContext => {
            if (retryContext.previousRetryCount === 0) {
              return 0
            }
            return Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000)
          }
        })
        .configureLogging(LogLevel.Information)
        .build()

      // Set up connection event handlers
      connection.onclose(error => {
        console.error('SignalR connection closed:', error)
      })

      connection.onreconnecting(error => {
        console.warn('SignalR reconnecting:', error)
      })

      connection.onreconnected(connectionId => {
        console.info('SignalR reconnected:', connectionId)
      })

      // Start the connection
      await connection.start()
      console.info('SignalR connection established')

      return connection
    } catch (error) {
      console.error('Failed to initialize SignalR connection:', error)
      throw new Error(`SignalR initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // ============================================================================
  // STORAGE & DATA ENDPOINTS
  // ============================================================================

  async bulkUpload(files: File[], options?: {
    batchSize?: number
    parallelProcessing?: boolean
    maxConcurrency?: number
    validateFiles?: boolean
    generateThumbnails?: boolean
    extractText?: boolean
    autoProcess?: boolean
  }): Promise<StorageOperation> {
    const formData = new FormData()

    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file)
    })

    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    const response = await this.instance.post<ApiResponse<StorageOperation>>(
      '/storage/bulk/upload',
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    )
    return response.data.data
  }

  async getStorageOperation(operationId: string): Promise<StorageOperation> {
    const response = await this.instance.get<ApiResponse<StorageOperation>>(
      `/storage/operations/${operationId}`
    )
    return response.data.data
  }

  // ============================================================================
  // ANALYTICS ENDPOINTS
  // ============================================================================

  async getAnalytics(params?: {
    organizationId?: string
    dateRange?: { start: string; end: string }
    metrics?: string[]
  }): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/analytics', { params })
    return response.data.data
  }

  async getAdvancedAnalytics(params?: {
    organizationId?: string
    dateRange?: { start: string; end: string }
    aggregation?: string
  }): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/analytics/advanced', { params })
    return response.data.data
  }

  // ============================================================================
  // INFRASTRUCTURE ENDPOINTS
  // ============================================================================

  async createApiKey(params: {
    name: string
    description?: string
    scopes: string[]
    expiresAt?: string
  }): Promise<{ id: string; key: string; keyPrefix: string }> {
    const response = await this.instance.post<ApiResponse<any>>('/infrastructure/api-keys', params)
    return response.data.data
  }

  async listApiKeys(): Promise<any[]> {
    const response = await this.instance.get<ApiResponse<any[]>>('/infrastructure/api-keys/list')
    return response.data.data
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  async healthCheck(): Promise<boolean> {
    try {
      await this.instance.get('/health')
      return true
    } catch {
      return false
    }
  }

  async downloadFile(url: string, filename?: string): Promise<void> {
    const response = await this.instance.get(url, { responseType: 'blob' })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  // ============================================================================
  // NOTIFICATION METHODS
  // ============================================================================

  async getNotifications(params?: {
    limit?: number
    includeRead?: boolean
    type?: string
    organizationId?: string
  }): Promise<{
    notifications: Array<{
      id: string
      title: string
      message: string
      type: string
      read: boolean
      createdAt: string
      data?: any
    }>
    unreadCount: number
    total: number
  }> {
    const response = await this.instance.get('/notifications', { params })
    return response.data
  }

  async markNotificationAsRead(notificationId: string): Promise<void> {
    await this.instance.post(`/notifications/${notificationId}/mark-read`)
  }

  async markAllNotificationsAsRead(): Promise<void> {
    await this.instance.post('/notifications/mark-all-read')
  }

  async deleteNotification(notificationId: string): Promise<void> {
    await this.instance.delete(`/notifications/${notificationId}`)
  }



  async clearAllNotifications(): Promise<void> {
    await this.instance.delete('/notifications/clear-all')
  }



  async getRateLimitStats(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/management/rate-limits/stats')
    return response.data.data
  }

  async getCircuitBreakerStatus(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/management/circuit-breakers')
    return response.data.data
  }

  async getInfrastructureConfig(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/management/configuration')
    return response.data.data
  }

  async updateInfrastructureConfig(config: Record<string, any>): Promise<any> {
    const response = await this.instance.put<ApiResponse<any>>('/management/configuration', config)
    return response.data.data
  }

  async createBackup(config: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/management/backups/create', config)
    return response.data.data
  }

  async getBackups(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/management/backups')
    return response.data.data
  }

  async getBackupStatus(backupId: string): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>(`/management/backups/${backupId}/status`)
    return response.data.data
  }

  async runHealthCheck(checkType?: string): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/management/health-checks/create', { checkType })
    return response.data.data
  }

  async getHealthCheckHistory(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/management/health-checks/history')
    return response.data.data
  }

  // ============================================================================
  // EVENT GRID METHODS
  // ============================================================================

  async publishEvent(eventData: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/eventgrid/publish', eventData)
    return response.data.data
  }

  async publishEvents(events: any[]): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/eventgrid/publish/batch', { events })
    return response.data.data
  }

  async getEventGridHealth(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/eventgrid/health')
    return response.data.data
  }

  async getEventMetrics(timeRange?: string): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/eventgrid/metrics', {
      params: { timeRange }
    })
    return response.data.data
  }

  async getEventMetricsSummary(timeRange?: string): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/eventgrid/metrics/summary', {
      params: { timeRange }
    })
    return response.data.data
  }

  async getEventSubscriptions(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/eventgrid/subscriptions')
    return response.data.data
  }

  async createEventSubscription(subscription: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/eventgrid/subscriptions', subscription)
    return response.data.data
  }

  async updateEventSubscription(id: string, subscription: any): Promise<any> {
    const response = await this.instance.put<ApiResponse<any>>(`/eventgrid/subscriptions/${id}`, subscription)
    return response.data.data
  }

  async deleteEventSubscription(id: string): Promise<void> {
    await this.instance.delete(`/eventgrid/subscriptions/${id}`)
  }

  async testEventSubscription(id: string): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>(`/eventgrid/subscriptions/${id}/test`)
    return response.data.data
  }

  async getEventSchemas(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/eventgrid/schemas')
    return response.data.data
  }

  async registerEventSchema(schema: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/eventgrid/schemas', schema)
    return response.data.data
  }

  async validateEvent(eventType: string, eventData: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/eventgrid/validate', { eventType, eventData })
    return response.data.data
  }

  async getEventHistory(params?: any): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/eventgrid/history', { params })
    return response.data.data
  }

  async replayFailedEvents(eventIds: string[]): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/eventgrid/replay', { eventIds })
    return response.data.data
  }

  async getDeadLetterEvents(params?: any): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/eventgrid/deadletter', { params })
    return response.data.data
  }

  async configureEventGrid(config: any): Promise<any> {
    const response = await this.instance.post<ApiResponse<any>>('/eventgrid/configure', config)
    return response.data.data
  }

  async getEventGridConfig(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/eventgrid/config')
    return response.data.data
  }

  // ============================================================================
  // SECURITY METHODS
  // ============================================================================

  async getSecuritySettings(): Promise<any> {
    const response = await this.instance.get<ApiResponse<any>>('/security/settings')
    return response.data.data
  }

  async updateSecuritySettings(settings: any): Promise<any> {
    const response = await this.instance.put<ApiResponse<any>>('/security/settings', settings)
    return response.data.data
  }

  // ============================================================================
  // GENERIC REQUEST METHOD
  // ============================================================================

  /**
   * Generic request method for flexibility
   */
  async request<T = any>(url: string, options?: {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
    body?: string
    headers?: Record<string, string>
    params?: Record<string, any>
    responseType?: 'blob' | 'json' | 'text'
  }): Promise<T> {
    const config: AxiosRequestConfig = {
      url,
      method: options?.method || 'GET',
      headers: options?.headers,
      params: options?.params,
      responseType: options?.responseType || 'json',
    }

    if (options?.body && ['POST', 'PUT', 'PATCH'].includes(options.method || 'GET')) {
      config.data = options.body
      config.headers = {
        ...config.headers,
        'Content-Type': 'application/json',
      }
    }

    const response = await this.instance.request<ApiResponse<T>>(config)
    return (response.data as any).data || response.data
  }

  // ============================================================================
  // SYSTEM MONITORING METHODS
  // ============================================================================

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<any> {
    return this.request('/monitoring/health')
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<any> {
    return this.request('/monitoring/performance')
  }

  /**
   * Get system alerts
   */
  async getSystemAlerts(): Promise<any> {
    return this.request('/monitoring/alerts')
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<any> {
    return this.request('/system/cache/stats')
  }

  /**
   * Clear cache
   */
  async clearCache(cacheKey?: string): Promise<any> {
    return this.request('/system/cache/clear', {
      method: 'POST',
      body: JSON.stringify({ cacheKey })
    })
  }

  // ============================================================================
  // ANALYTICS METHODS
  // ============================================================================

  /**
   * Get comprehensive analytics
   */
  async getComprehensiveAnalytics(options: any): Promise<any> {
    return this.request('/analytics/comprehensive', {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  /**
   * Get real-time analytics
   */
  async getRealTimeAnalytics(): Promise<any> {
    return this.request('/analytics/realtime')
  }

  /**
   * Generate custom report
   */
  async generateReport(reportConfig: any): Promise<any> {
    return this.request('/reports/generate', {
      method: 'POST',
      body: JSON.stringify(reportConfig)
    })
  }

  /**
   * Export data
   */
  async exportData(exportConfig: any): Promise<any> {
    return this.request('/data/export', {
      method: 'POST',
      body: JSON.stringify(exportConfig)
    })
  }

  // ============================================================================
  // MOBILE DEVICE METHODS
  // ============================================================================

  /**
   * Get mobile devices
   */
  async getMobileDevices(): Promise<any> {
    return this.request('/mobile/devices')
  }

  /**
   * Register mobile device
   */
  async registerMobileDevice(deviceData: any): Promise<any> {
    return this.request('/mobile/devices/register', {
      method: 'POST',
      body: JSON.stringify(deviceData)
    })
  }

  /**
   * Send mobile notification
   */
  async sendMobileNotification(notificationData: any): Promise<any> {
    return this.request('/mobile/notifications/send', {
      method: 'POST',
      body: JSON.stringify(notificationData)
    })
  }

  /**
   * Sync mobile device
   */
  async syncMobileDevice(deviceId: string): Promise<any> {
    return this.request('/mobile/devices/sync', {
      method: 'POST',
      body: JSON.stringify({ deviceId })
    })
  }

  /**
   * Get offline data for device
   */
  async getOfflineData(deviceId: string): Promise<any> {
    return this.request(`/mobile/offline-data?deviceId=${deviceId}`)
  }

  /**
   * Get mobile sync status
   */
  async getMobileSyncStatus(): Promise<any> {
    return this.request('/mobile/sync/status')
  }

  // ============================================================================
  // INFRASTRUCTURE METHODS
  // ============================================================================

  /**
   * Get system metrics
   */
  async getSystemMetrics(): Promise<any> {
    return this.request('/system/metrics')
  }

  /**
   * Collect metrics
   */
  async collectMetrics(metricsData: any): Promise<any> {
    return this.request('/metrics/collect', {
      method: 'POST',
      body: JSON.stringify(metricsData)
    })
  }

  /**
   * Query metrics
   */
  async queryMetrics(query: any): Promise<any> {
    return this.request('/metrics/query', {
      method: 'POST',
      body: JSON.stringify(query)
    })
  }

  /**
   * Get metrics summary
   */
  async getMetricsSummary(): Promise<any> {
    return this.request('/metrics/summary')
  }

  /**
   * Get logs
   */
  async getLogs(query: any): Promise<any> {
    return this.request('/logs/query', {
      method: 'POST',
      body: JSON.stringify(query)
    })
  }

  /**
   * Get log statistics
   */
  async getLogStatistics(): Promise<any> {
    return this.request('/logs/statistics')
  }

  /**
   * Create log entry
   */
  async createLog(logData: any): Promise<any> {
    return this.request('/logs', {
      method: 'POST',
      body: JSON.stringify(logData)
    })
  }

  // ============================================================================
  // COMPLIANCE AND AUDIT METHODS
  // ============================================================================

  /**
   * Get compliance assessments
   */
  async getComplianceAssessments(): Promise<any> {
    return this.request('/compliance/assessments')
  }

  /**
   * Create compliance assessment
   */
  async createComplianceAssessment(assessmentData: any): Promise<any> {
    return this.request('/compliance/assessments', {
      method: 'POST',
      body: JSON.stringify(assessmentData)
    })
  }

  /**
   * Update compliance status
   */
  async updateComplianceStatus(statusData: any): Promise<any> {
    return this.request('/compliance/status', {
      method: 'PUT',
      body: JSON.stringify(statusData)
    })
  }

  // ============================================================================
  // FILE PROCESSING METHODS
  // ============================================================================

  /**
   * Process file
   */
  async processFile(fileData: any): Promise<any> {
    return this.request('/files/process', {
      method: 'POST',
      body: JSON.stringify(fileData)
    })
  }

  /**
   * Get file processing status
   */
  async getFileProcessingStatus(jobId: string): Promise<any> {
    return this.request(`/files/processing/${jobId}/status`)
  }

  // ============================================================================
  // STORAGE METHODS
  // ============================================================================

  /**
   * Configure storage
   */
  async configureStorage(storageConfig: any): Promise<any> {
    return this.request('/storage/configure', {
      method: 'POST',
      body: JSON.stringify(storageConfig)
    })
  }

  /**
   * Sync document to storage
   */
  async syncDocumentToStorage(documentId: string): Promise<any> {
    return this.request('/storage/sync/document', {
      method: 'POST',
      body: JSON.stringify({ documentId })
    })
  }

  /**
   * Bulk sync to storage
   */
  async bulkSyncToStorage(syncData: any): Promise<any> {
    return this.request('/storage/sync/bulk', {
      method: 'POST',
      body: JSON.stringify(syncData)
    })
  }
}

// Create and export singleton instance
export const backendApiClient = new BackendApiClient()

// Export the class for advanced usage
export { BackendApiClient }
export default backendApiClient
