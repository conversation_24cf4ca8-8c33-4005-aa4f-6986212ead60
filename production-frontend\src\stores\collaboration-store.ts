/**
 * Collaboration Store - Production Ready
 * Manages real-time collaboration sessions, participants, and SignalR connections
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { backendApiClient } from '../services/backend-api-client'
import type {
  CollaborationSession,
  SessionParticipant,
  SessionSettings,
  CursorPosition
} from '../types/backend'

// Define SessionStatus type
export type SessionStatus = 'ACTIVE' | 'INACTIVE' | 'PAUSED' | 'ENDED'

export interface CollaborationState {
  // Session state
  currentSession: CollaborationSession | null
  sessions: CollaborationSession[]
  participants: SessionParticipant[]
  
  // Real-time state
  isConnected: boolean
  connectionId: string | null
  signalRConnection: any | null
  
  // User presence
  activeUsers: Map<string, {
    userId: string
    name: string
    avatar?: string
    cursor?: CursorPosition
    lastSeen: string
    isTyping: boolean
  }>
  
  // Document collaboration
  documentLocks: Map<string, {
    userId: string
    lockedAt: string
    section?: string
  }>
  
  // Messages and notifications
  messages: Array<{
    id: string
    userId: string
    userName: string
    content: string
    timestamp: string
    type: 'message' | 'system' | 'notification'
  }>
  
  // UI state
  loading: boolean
  error: string | null
  
  // Settings
  settings: {
    enableRealTimeUpdates: boolean
    showCursors: boolean
    showPresence: boolean
    enableNotifications: boolean
    autoSave: boolean
    autoSaveInterval: number
  }
}

export interface CollaborationActions {
  // Session management
  createSession: (params: {
    documentId: string
    name: string
    description?: string
    maxParticipants?: number
    settings?: Partial<SessionSettings>
  }) => Promise<CollaborationSession>
  
  joinSession: (sessionId: string) => Promise<void>
  leaveSession: (sessionId: string) => Promise<void>
  endSession: (sessionId: string) => Promise<void>
  
  // Connection management
  connect: () => Promise<void>
  disconnect: () => Promise<void>
  reconnect: () => Promise<void>
  
  // Participant management
  updateParticipant: (userId: string, updates: Partial<SessionParticipant>) => void
  removeParticipant: (userId: string) => void
  
  // Real-time updates
  updateCursor: (position: CursorPosition) => void
  updatePresence: (isActive: boolean) => void
  setTyping: (isTyping: boolean) => void
  
  // Document collaboration
  lockDocument: (documentId: string, section?: string) => Promise<void>
  unlockDocument: (documentId: string, section?: string) => Promise<void>
  requestDocumentAccess: (documentId: string) => Promise<boolean>
  
  // Messaging
  sendMessage: (content: string, type?: 'message' | 'system' | 'notification') => Promise<void>
  clearMessages: () => void
  
  // Settings
  updateSettings: (settings: Partial<CollaborationState['settings']>) => void
  
  // Utility methods
  isUserOnline: (userId: string) => boolean
  getParticipantCount: () => number
  canUserEdit: (userId: string) => boolean
  
  // State management
  clearError: () => void
  reset: () => void
}

export type CollaborationStore = CollaborationState & CollaborationActions

export const useCollaborationStore = create<CollaborationStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSession: null,
      sessions: [],
      participants: [],
      isConnected: false,
      connectionId: null,
      signalRConnection: null,
      activeUsers: new Map(),
      documentLocks: new Map(),
      messages: [],
      loading: false,
      error: null,
      settings: {
        enableRealTimeUpdates: true,
        showCursors: true,
        showPresence: true,
        enableNotifications: true,
        autoSave: true,
        autoSaveInterval: 30000, // 30 seconds
      },

      // Session management
      createSession: async (params) => {
        set({ loading: true, error: null })

        try {
          const session = await backendApiClient.createCollaborationSession(params)
          
          const { sessions } = get()
          
          set({
            currentSession: session,
            sessions: [session, ...sessions],
            loading: false,
          })

          // Auto-join the created session
          await get().joinSession(session.id)

          return session
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to create collaboration session',
          })
          throw error
        }
      },

      joinSession: async (sessionId) => {
        set({ loading: true, error: null })

        try {
          await backendApiClient.joinCollaborationSession(sessionId)
          
          const session = await backendApiClient.getCollaborationSession(sessionId)
          
          set({
            currentSession: session,
            participants: session.participants,
            loading: false,
          })

          // Connect to SignalR if not already connected
          if (!get().isConnected) {
            await get().connect()
          }

          // Update presence
          get().updatePresence(true)
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to join collaboration session',
          })
          throw error
        }
      },

      leaveSession: async (sessionId) => {
        set({ loading: true, error: null })

        try {
          await backendApiClient.leaveCollaborationSession(sessionId)
          
          set({
            currentSession: null,
            participants: [],
            activeUsers: new Map(),
            documentLocks: new Map(),
            messages: [],
            loading: false,
          })

          // Disconnect from SignalR
          await get().disconnect()
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to leave collaboration session',
          })
          throw error
        }
      },

      endSession: async (sessionId) => {
        set({ loading: true, error: null })

        try {
          await backendApiClient.endCollaborationSession(sessionId)
          
          const { sessions } = get()
          
          set({
            currentSession: null,
            sessions: sessions.filter(s => s.id !== sessionId),
            participants: [],
            activeUsers: new Map(),
            documentLocks: new Map(),
            messages: [],
            loading: false,
          })

          await get().disconnect()
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to end collaboration session',
          })
          throw error
        }
      },

      // Connection management
      connect: async () => {
        const { settings } = get()
        
        if (!settings.enableRealTimeUpdates) {
          return
        }

        try {
          // Initialize SignalR connection through backend API client
          const connection = await backendApiClient.initializeSignalR()
          
          // Set up event handlers
          connection.on('UserJoined', (participant: SessionParticipant) => {
            const { participants, activeUsers } = get()
            
            set({
              participants: [...participants, participant],
              activeUsers: new Map(activeUsers.set(participant.userId, {
                userId: participant.userId,
                name: participant.userId, // Will be updated with actual name
                lastSeen: new Date().toISOString(),
                isTyping: false,
              })),
            })
          })

          connection.on('UserLeft', (userId: string) => {
            get().removeParticipant(userId)
          })

          connection.on('CursorUpdate', (userId: string, position: CursorPosition) => {
            const { activeUsers } = get()
            const user = activeUsers.get(userId)
            
            if (user) {
              activeUsers.set(userId, { ...user, cursor: position })
              set({ activeUsers: new Map(activeUsers) })
            }
          })

          connection.on('DocumentLocked', (documentId: string, userId: string, section?: string) => {
            const { documentLocks } = get()
            documentLocks.set(`${documentId}:${section || 'full'}`, {
              userId,
              lockedAt: new Date().toISOString(),
              section,
            })
            set({ documentLocks: new Map(documentLocks) })
          })

          connection.on('DocumentUnlocked', (documentId: string, section?: string) => {
            const { documentLocks } = get()
            documentLocks.delete(`${documentId}:${section || 'full'}`)
            set({ documentLocks: new Map(documentLocks) })
          })

          connection.on('MessageReceived', (message: any) => {
            const { messages } = get()
            set({
              messages: [...messages, {
                id: message.id || `msg_${Date.now()}`,
                userId: message.userId,
                userName: message.userName,
                content: message.content,
                timestamp: message.timestamp || new Date().toISOString(),
                type: message.type || 'message',
              }],
            })
          })

          await connection.start()
          
          set({
            signalRConnection: connection,
            isConnected: true,
            connectionId: connection.connectionId,
            error: null,
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to connect to collaboration service',
            isConnected: false,
          })
          throw error
        }
      },

      disconnect: async () => {
        const { signalRConnection } = get()
        
        if (signalRConnection) {
          try {
            await signalRConnection.stop()
          } catch (error) {
            console.warn('Error stopping SignalR connection:', error)
          }
        }

        set({
          signalRConnection: null,
          isConnected: false,
          connectionId: null,
          activeUsers: new Map(),
          documentLocks: new Map(),
        })
      },

      reconnect: async () => {
        await get().disconnect()
        await get().connect()
      },

      // Participant management
      updateParticipant: (userId, updates) => {
        const { participants } = get()
        
        set({
          participants: participants.map(p => 
            p.userId === userId ? { ...p, ...updates } : p
          ),
        })
      },

      removeParticipant: (userId) => {
        const { participants, activeUsers } = get()
        
        activeUsers.delete(userId)
        
        set({
          participants: participants.filter(p => p.userId !== userId),
          activeUsers: new Map(activeUsers),
        })
      },

      // Real-time updates
      updateCursor: (position) => {
        const { signalRConnection, currentSession } = get()
        
        if (signalRConnection && currentSession) {
          signalRConnection.invoke('UpdateCursor', currentSession.id, position)
            .catch((error: any) => console.error('Failed to update cursor:', error))
        }
      },

      updatePresence: (isActive) => {
        const { signalRConnection, currentSession } = get()
        
        if (signalRConnection && currentSession) {
          signalRConnection.invoke('UpdatePresence', currentSession.id, isActive)
            .catch((error: any) => console.error('Failed to update presence:', error))
        }
      },

      setTyping: (isTyping) => {
        const { signalRConnection, currentSession } = get()
        
        if (signalRConnection && currentSession) {
          signalRConnection.invoke('SetTyping', currentSession.id, isTyping)
            .catch((error: any) => console.error('Failed to set typing status:', error))
        }
      },

      // Document collaboration
      lockDocument: async (documentId, section) => {
        const { signalRConnection, currentSession } = get()
        
        if (signalRConnection && currentSession) {
          try {
            await signalRConnection.invoke('LockDocument', currentSession.id, documentId, section)
          } catch (error: any) {
            throw new Error(`Failed to lock document: ${error.message}`)
          }
        }
      },

      unlockDocument: async (documentId, section) => {
        const { signalRConnection, currentSession } = get()
        
        if (signalRConnection && currentSession) {
          try {
            await signalRConnection.invoke('UnlockDocument', currentSession.id, documentId, section)
          } catch (error: any) {
            throw new Error(`Failed to unlock document: ${error.message}`)
          }
        }
      },

      requestDocumentAccess: async (documentId) => {
        const { signalRConnection, currentSession } = get()
        
        if (signalRConnection && currentSession) {
          try {
            return await signalRConnection.invoke('RequestDocumentAccess', currentSession.id, documentId)
          } catch (error: any) {
            throw new Error(`Failed to request document access: ${error.message}`)
          }
        }
        
        return false
      },

      // Messaging
      sendMessage: async (content, type = 'message') => {
        const { signalRConnection, currentSession } = get()
        
        if (signalRConnection && currentSession) {
          try {
            await signalRConnection.invoke('SendMessage', currentSession.id, content, type)
          } catch (error: any) {
            throw new Error(`Failed to send message: ${error.message}`)
          }
        }
      },

      clearMessages: () => {
        set({ messages: [] })
      },

      // Settings
      updateSettings: (newSettings) => {
        const { settings } = get()
        
        set({
          settings: { ...settings, ...newSettings },
        })
      },

      // Utility methods
      isUserOnline: (userId) => {
        const { activeUsers } = get()
        const user = activeUsers.get(userId)
        
        if (!user) return false
        
        const lastSeen = new Date(user.lastSeen).getTime()
        const now = Date.now()
        
        // Consider user online if last seen within 5 minutes
        return (now - lastSeen) < 5 * 60 * 1000
      },

      getParticipantCount: () => {
        return get().participants.length
      },

      canUserEdit: (userId) => {
        const { participants } = get()
        const participant = participants.find(p => p.userId === userId)
        
        return participant?.permissions.includes('write') || false
      },

      // State management
      clearError: () => {
        set({ error: null })
      },

      reset: () => {
        get().disconnect()
        
        set({
          currentSession: null,
          sessions: [],
          participants: [],
          isConnected: false,
          connectionId: null,
          signalRConnection: null,
          activeUsers: new Map(),
          documentLocks: new Map(),
          messages: [],
          loading: false,
          error: null,
        })
      },
    }),
    {
      name: 'collaboration-store-v1',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        sessions: state.sessions.slice(-10), // Keep last 10 sessions
        settings: state.settings,
      }),
    }
  )
)

// ============================================================================
// SELECTOR HOOKS
// ============================================================================

// Core selectors
export const useCurrentSession = () => useCollaborationStore((state) => state.currentSession)
export const useCollaborationSessions = () => useCollaborationStore((state) => state.sessions)
export const useSessionParticipants = () => useCollaborationStore((state) => state.participants)
export const useCollaborationLoading = () => useCollaborationStore((state) => state.loading)
export const useCollaborationError = () => useCollaborationStore((state) => state.error)

// Connection state selectors
export const useIsCollaborationConnected = () => useCollaborationStore((state) => state.isConnected)
export const useConnectionId = () => useCollaborationStore((state) => state.connectionId)

// Real-time state selectors
export const useActiveUsers = () => useCollaborationStore((state) => state.activeUsers)
export const useDocumentLocks = () => useCollaborationStore((state) => state.documentLocks)
export const useCollaborationMessages = () => useCollaborationStore((state) => state.messages)

// Settings selectors
export const useCollaborationSettings = () => useCollaborationStore((state) => state.settings)

// Action hooks
export const useCreateCollaborationSession = () => useCollaborationStore((state) => state.createSession)
export const useJoinCollaborationSession = () => useCollaborationStore((state) => state.joinSession)
export const useLeaveCollaborationSession = () => useCollaborationStore((state) => state.leaveSession)
export const useEndCollaborationSession = () => useCollaborationStore((state) => state.endSession)

// Connection management hooks
export const useConnectCollaboration = () => useCollaborationStore((state) => state.connect)
export const useDisconnectCollaboration = () => useCollaborationStore((state) => state.disconnect)
export const useReconnectCollaboration = () => useCollaborationStore((state) => state.reconnect)

// Real-time action hooks
export const useUpdateCursor = () => useCollaborationStore((state) => state.updateCursor)
export const useUpdatePresence = () => useCollaborationStore((state) => state.updatePresence)
export const useSetTyping = () => useCollaborationStore((state) => state.setTyping)

// Document collaboration hooks
export const useLockDocument = () => useCollaborationStore((state) => state.lockDocument)
export const useUnlockDocument = () => useCollaborationStore((state) => state.unlockDocument)
export const useRequestDocumentAccess = () => useCollaborationStore((state) => state.requestDocumentAccess)

// Messaging hooks
export const useSendMessage = () => useCollaborationStore((state) => state.sendMessage)
export const useClearMessages = () => useCollaborationStore((state) => state.clearMessages)

// Utility hooks
export const useCollaborationUtils = () => {
  const isUserOnline = useCollaborationStore((state) => state.isUserOnline)
  const getParticipantCount = useCollaborationStore((state) => state.getParticipantCount)
  const canUserEdit = useCollaborationStore((state) => state.canUserEdit)

  return {
    isUserOnline,
    getParticipantCount,
    canUserEdit,
  }
}

// Session status hook
export const useSessionStatus = () => {
  const currentSession = useCurrentSession()
  const isConnected = useIsCollaborationConnected()
  const participants = useSessionParticipants()
  const activeUsers = useActiveUsers()

  return {
    hasActiveSession: !!currentSession,
    isConnected,
    sessionId: currentSession?.id || null,
    participantCount: participants.length,
    activeUserCount: activeUsers.size,
    sessionStatus: currentSession?.status || null,
  }
}

// Document lock status hook
export const useDocumentLockStatus = (documentId: string, section?: string) => {
  const documentLocks = useDocumentLocks()
  const lockKey = `${documentId}:${section || 'full'}`
  const lock = documentLocks.get(lockKey)

  return {
    isLocked: !!lock,
    lockedBy: lock?.userId || null,
    lockedAt: lock?.lockedAt || null,
    section: lock?.section || null,
  }
}

// User presence hook
export const useUserPresence = (userId: string) => {
  const activeUsers = useActiveUsers()
  const isUserOnline = useCollaborationStore((state) => state.isUserOnline)
  const user = activeUsers.get(userId)

  return {
    isOnline: isUserOnline(userId),
    cursor: user?.cursor || null,
    isTyping: user?.isTyping || false,
    lastSeen: user?.lastSeen || null,
    name: user?.name || null,
    avatar: user?.avatar || null,
  }
}

// Collaboration permissions hook
export const useCollaborationPermissions = (userId?: string) => {
  const participants = useSessionParticipants()
  const canUserEdit = useCollaborationStore((state) => state.canUserEdit)
  const currentSession = useCurrentSession()

  const participant = userId
    ? participants.find(p => p.userId === userId)
    : null

  return {
    canEdit: userId ? canUserEdit(userId) : false,
    canManage: participant?.role === 'owner' || participant?.role === 'admin',
    permissions: participant?.permissions || [],
    role: participant?.role || null,
    isOwner: currentSession?.createdBy === userId,
  }
}
